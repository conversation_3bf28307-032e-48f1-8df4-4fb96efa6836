<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء ترخيص تجريبي - مؤسسة وقود المستقبل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.8rem;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }

        select, input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            display: none;
        }

        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .license-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            text-align: right;
        }

        .license-key {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            background: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            word-break: break-all;
        }

        .actions {
            margin-top: 20px;
        }

        .back-btn {
            background: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="fas fa-key"></i>
        </div>
        
        <h1>إنشاء ترخيص تجريبي</h1>
        <p class="subtitle">أنشئ ترخيص تجريبي للاختبار</p>

        <form id="licenseForm">
            <div class="form-group">
                <label for="licenseType">نوع الترخيص:</label>
                <select id="licenseType" name="licenseType">
                    <option value="demo">تجريبي (30 يوم)</option>
                    <option value="basic">أساسي (365 يوم)</option>
                    <option value="premium">مميز (365 يوم)</option>
                    <option value="enterprise">مؤسسي (365 يوم)</option>
                </select>
            </div>

            <div class="form-group">
                <label for="duration">مدة الترخيص (بالأيام):</label>
                <input type="number" id="duration" name="duration" value="30" min="1" max="3650">
            </div>

            <button type="submit" class="btn" id="createBtn">
                إنشاء الترخيص
            </button>
        </form>

        <div id="result" class="result">
            <div id="resultMessage"></div>
            <div id="licenseDetails" class="license-info" style="display: none;">
                <h3>تفاصيل الترخيص:</h3>
                <div id="licenseKey" class="license-key"></div>
                <p><strong>النوع:</strong> <span id="licenseTypeDisplay"></span></p>
                <p><strong>تاريخ التفعيل:</strong> <span id="activationDate"></span></p>
                <p><strong>تاريخ الانتهاء:</strong> <span id="expiryDate"></span></p>
                <p><strong>المدة:</strong> <span id="durationDisplay"></span> يوم</p>
            </div>
        </div>

        <div class="actions">
            <button class="btn back-btn" onclick="window.location.href='src/auth/login.html'">
                العودة لتسجيل الدخول
            </button>
        </div>
    </div>

    <script src="src/auth/license.js"></script>
    <script>
        document.getElementById('licenseForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const createBtn = document.getElementById('createBtn');
            const result = document.getElementById('result');
            const resultMessage = document.getElementById('resultMessage');
            const licenseDetails = document.getElementById('licenseDetails');
            
            // تعطيل الزر
            createBtn.disabled = true;
            createBtn.textContent = 'جاري الإنشاء...';
            
            try {
                const licenseType = document.getElementById('licenseType').value;
                const duration = parseInt(document.getElementById('duration').value);
                
                // إنشاء الترخيص
                const license = window.licenseSystem.createTestLicense(licenseType, duration);
                
                // عرض النتيجة
                result.className = 'result success';
                result.style.display = 'block';
                resultMessage.textContent = '✅ تم إنشاء الترخيص بنجاح!';
                
                // عرض تفاصيل الترخيص
                document.getElementById('licenseKey').textContent = license.key;
                document.getElementById('licenseTypeDisplay').textContent = license.type;
                document.getElementById('activationDate').textContent = new Date(license.activationDate).toLocaleDateString('ar-DZ');
                document.getElementById('expiryDate').textContent = new Date(license.expiryDate).toLocaleDateString('ar-DZ');
                document.getElementById('durationDisplay').textContent = duration;
                
                licenseDetails.style.display = 'block';
                
            } catch (error) {
                result.className = 'result error';
                result.style.display = 'block';
                resultMessage.textContent = '❌ حدث خطأ: ' + error.message;
                licenseDetails.style.display = 'none';
            } finally {
                createBtn.disabled = false;
                createBtn.textContent = 'إنشاء الترخيص';
            }
        });

        // تحديث المدة عند تغيير النوع
        document.getElementById('licenseType').addEventListener('change', function() {
            const type = this.value;
            const durationInput = document.getElementById('duration');
            
            switch(type) {
                case 'demo':
                    durationInput.value = 30;
                    break;
                case 'basic':
                case 'premium':
                case 'enterprise':
                    durationInput.value = 365;
                    break;
            }
        });
    </script>
</body>
</html>
