<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المبسطة - CFGPLProgram</title>
    <link rel="stylesheet" href="admin-panel.css">
    <style>
        /* أنماط مبسطة للاختبار */
        .content-section {
            display: none;
            padding: 20px;
            background: white;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .content-section.active {
            display: block;
        }
        
        .nav-item {
            padding: 10px 15px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-item:hover {
            background: #e9ecef;
        }
        
        .nav-item.active {
            background: #007bff;
            color: white;
        }
        
        .test-status {
            padding: 20px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            color: #155724;
            margin: 10px 0;
        }
        
        .sidebar {
            width: 250px;
            background: #343a40;
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .main-content {
            margin-right: 270px;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <h2>لوحة التحكم</h2>
        
        <div class="nav-item active" data-section="dashboard">
            <i class="fas fa-tachometer-alt"></i>
            لوحة المعلومات
        </div>
        
        <div class="nav-item" data-section="licenses">
            <i class="fas fa-key"></i>
            إدارة التراخيص
        </div>
        
        <div class="nav-item" data-section="clients">
            <i class="fas fa-users"></i>
            العملاء
        </div>
        
        <div class="nav-item" data-section="activations">
            <i class="fas fa-clipboard-list"></i>
            طلبات التفعيل
        </div>
        
        <div class="nav-item" data-section="monitoring">
            <i class="fas fa-chart-line"></i>
            المراقبة
        </div>
        
        <div class="nav-item" data-section="settings">
            <i class="fas fa-cog"></i>
            الإعدادات
        </div>
    </div>
    
    <div class="main-content">
        <div class="header">
            <h1 id="page-title">لوحة المعلومات</h1>
            <p>نسخة مبسطة لاختبار الوظائف الأساسية</p>
        </div>
        
        <!-- لوحة المعلومات -->
        <section id="dashboard-section" class="content-section active">
            <h2>📊 لوحة المعلومات</h2>
            <div class="test-status">
                ✅ قسم لوحة المعلومات يعمل بشكل صحيح
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center;">
                    <h3>التراخيص النشطة</h3>
                    <p style="font-size: 2rem; font-weight: bold; color: #1976d2;">15</p>
                </div>
                <div style="background: #f3e5f5; padding: 20px; border-radius: 10px; text-align: center;">
                    <h3>العملاء</h3>
                    <p style="font-size: 2rem; font-weight: bold; color: #7b1fa2;">42</p>
                </div>
                <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center;">
                    <h3>طلبات التفعيل</h3>
                    <p style="font-size: 2rem; font-weight: bold; color: #388e3c;">8</p>
                </div>
            </div>
        </section>
        
        <!-- إدارة التراخيص -->
        <section id="licenses-section" class="content-section">
            <h2>🔑 إدارة التراخيص</h2>
            <div class="test-status">
                ✅ قسم إدارة التراخيص يعمل بشكل صحيح
            </div>
            
            <div style="margin: 20px 0;">
                <button style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px;">
                    إنشاء ترخيص جديد
                </button>
                <button style="background: #17a2b8; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px;">
                    إنشاء ترخيص تجريبي
                </button>
                <button style="background: #ffc107; color: black; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px;">
                    تصدير التراخيص
                </button>
            </div>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                <p>هنا ستظهر جدول التراخيص مع إمكانيات الفلترة والبحث</p>
            </div>
        </section>
        
        <!-- العملاء -->
        <section id="clients-section" class="content-section">
            <h2>👥 العملاء</h2>
            <div class="test-status">
                ✅ قسم العملاء يعمل بشكل صحيح
            </div>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                <p>هنا ستظهر قائمة العملاء مع إمكانيات الإدارة</p>
            </div>
        </section>
        
        <!-- طلبات التفعيل -->
        <section id="activations-section" class="content-section">
            <h2>📋 طلبات التفعيل</h2>
            <div class="test-status">
                ✅ قسم طلبات التفعيل يعمل بشكل صحيح
            </div>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                <p>هنا ستظهر طلبات التفعيل الجديدة</p>
            </div>
        </section>
        
        <!-- المراقبة -->
        <section id="monitoring-section" class="content-section">
            <h2>📈 المراقبة</h2>
            <div class="test-status">
                ✅ قسم المراقبة يعمل بشكل صحيح
            </div>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                <p>هنا ستظهر بيانات المراقبة والتحليلات</p>
            </div>
        </section>
        
        <!-- الإعدادات -->
        <section id="settings-section" class="content-section">
            <h2>⚙️ الإعدادات</h2>
            <div class="test-status">
                ✅ قسم الإعدادات يعمل بشكل صحيح
            </div>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                <p>هنا ستظهر إعدادات النظام</p>
            </div>
        </section>
        
        <!-- معلومات التشخيص -->
        <div style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin-top: 30px;">
            <h3>🔧 معلومات التشخيص</h3>
            <p><strong>حالة النظام:</strong> <span style="color: #28a745;">✅ يعمل بشكل طبيعي</span></p>
            <p><strong>القسم الحالي:</strong> <span id="current-section-display">dashboard</span></p>
            <p><strong>عدد الأقسام:</strong> <span id="sections-count">6</span></p>
            <p><strong>آخر تحديث:</strong> <span id="last-update"></span></p>
        </div>
    </div>

    <script>
        // نظام تنقل مبسط
        class SimpleAdminPanel {
            constructor() {
                this.currentSection = 'dashboard';
                this.init();
            }
            
            init() {
                console.log('🚀 تهيئة لوحة التحكم المبسطة...');
                this.setupNavigation();
                this.updateDiagnosticInfo();
                
                // تحديث الوقت كل ثانية
                setInterval(() => this.updateTime(), 1000);
            }
            
            setupNavigation() {
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        const section = item.dataset.section;
                        this.switchSection(section);
                    });
                });
            }
            
            switchSection(sectionName) {
                console.log(`🔄 التبديل إلى القسم: ${sectionName}`);
                
                // إخفاء جميع الأقسام
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                });
                
                // إظهار القسم المحدد
                const targetSection = document.getElementById(`${sectionName}-section`);
                if (targetSection) {
                    targetSection.classList.add('active');
                    console.log(`✅ تم إظهار القسم: ${sectionName}`);
                } else {
                    console.error(`❌ لم يتم العثور على القسم: ${sectionName}`);
                    return;
                }
                
                // تحديث التنقل
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`);
                if (activeNavItem) {
                    activeNavItem.classList.add('active');
                }
                
                // تحديث العنوان
                const titles = {
                    dashboard: 'لوحة المعلومات',
                    licenses: 'إدارة التراخيص',
                    clients: 'العملاء',
                    activations: 'طلبات التفعيل',
                    monitoring: 'المراقبة',
                    settings: 'الإعدادات'
                };
                
                document.getElementById('page-title').textContent = titles[sectionName] || 'لوحة التحكم';
                this.currentSection = sectionName;
                
                // تحديث معلومات التشخيص
                this.updateDiagnosticInfo();
            }
            
            updateDiagnosticInfo() {
                document.getElementById('current-section-display').textContent = this.currentSection;
                document.getElementById('sections-count').textContent = document.querySelectorAll('.content-section').length;
            }
            
            updateTime() {
                document.getElementById('last-update').textContent = new Date().toLocaleTimeString('ar-DZ');
            }
        }
        
        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', () => {
            window.simpleAdminPanel = new SimpleAdminPanel();
            console.log('✅ تم تهيئة لوحة التحكم المبسطة بنجاح');
        });
    </script>
</body>
</html>
