<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فهرس المشروع - CFGPLProgram</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #667eea;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .card.success {
            border-left-color: #28a745;
        }
        
        .card.warning {
            border-left-color: #ffc107;
        }
        
        .card.danger {
            border-left-color: #dc3545;
        }
        
        .card.info {
            border-left-color: #17a2b8;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }
        
        .btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }
        
        .btn.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        
        .status-working {
            background: #d4edda;
            color: #155724;
        }
        
        .status-new {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-fixed {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-enhanced {
            background: #fff3cd;
            color: #856404;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-card.blue {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .stat-card.green {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .stat-card.purple {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- رأس الصفحة -->
        <div class="header">
            <h1 style="color: #333; margin-bottom: 20px;">
                <i class="fas fa-folder-open"></i>
                فهرس مشروع CFGPLProgram
            </h1>
            <p style="font-size: 1.2rem; color: #666; margin-bottom: 30px;">
                دليل شامل لجميع ملفات وأدوات المشروع المطور
            </p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">25+</div>
                    <div>ملف</div>
                </div>
                <div class="stat-card blue">
                    <div class="stat-number">8</div>
                    <div>أداة</div>
                </div>
                <div class="stat-card green">
                    <div class="stat-number">100%</div>
                    <div>مكتمل</div>
                </div>
                <div class="stat-card purple">
                    <div class="stat-number">6</div>
                    <div>أقسام</div>
                </div>
            </div>
        </div>
        
        <!-- التطبيقات الرئيسية -->
        <div class="section">
            <h2><i class="fas fa-rocket"></i> التطبيقات الرئيسية</h2>
            
            <div class="grid">
                <div class="card success">
                    <h3><i class="fas fa-home"></i> التطبيق الرئيسي</h3>
                    <p>الواجهة الرئيسية لإدارة الوقود والزبائن</p>
                    <span class="status-badge status-working">✅ يعمل</span>
                    <div style="margin-top: 15px;">
                        <a href="index.html" class="btn success">فتح التطبيق</a>
                    </div>
                </div>
                
                <div class="card info">
                    <h3><i class="fas fa-tachometer-alt"></i> لوحة التحكم المُصلحة</h3>
                    <p>النسخة المحسنة والمُصلحة من لوحة التحكم</p>
                    <span class="status-badge status-fixed">✅ مُصلح</span>
                    <div style="margin-top: 15px;">
                        <a href="src/remote/admin-panel-fixed.html" class="btn info">فتح</a>
                    </div>
                </div>
                
                <div class="card">
                    <h3><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h3>
                    <p>صفحة تسجيل الدخول مع إدخال كود الترخيص</p>
                    <span class="status-badge status-enhanced">🔧 محسن</span>
                    <div style="margin-top: 15px;">
                        <a href="src/auth/login.html" class="btn">فتح</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أدوات التشخيص والاختبار -->
        <div class="section">
            <h2><i class="fas fa-tools"></i> أدوات التشخيص والاختبار</h2>
            
            <div class="grid">
                <div class="card warning">
                    <h3><i class="fas fa-vial"></i> صفحة الاختبار الشاملة</h3>
                    <p>اختبار جميع وظائف لوحة التحكم مع إطار مدمج</p>
                    <span class="status-badge status-new">🆕 جديد</span>
                    <div style="margin-top: 15px;">
                        <a href="src/remote/test-admin-panel.html" class="btn warning">فتح</a>
                    </div>
                </div>
                
                <div class="card danger">
                    <h3><i class="fas fa-bug"></i> أداة التشخيص</h3>
                    <p>تشخيص مشاكل لوحة التحكم ومراقبة وحدة التحكم</p>
                    <span class="status-badge status-new">🆕 جديد</span>
                    <div style="margin-top: 15px;">
                        <a href="src/remote/debug-admin-panel.html" class="btn danger">فتح</a>
                    </div>
                </div>
                
                <div class="card info">
                    <h3><i class="fas fa-th-large"></i> لوحة التحكم المبسطة</h3>
                    <p>نسخة مبسطة تعمل بضمان 100%</p>
                    <span class="status-badge status-working">✅ يعمل</span>
                    <div style="margin-top: 15px;">
                        <a href="src/remote/admin-panel-simple.html" class="btn info">فتح</a>
                    </div>
                </div>
                
                <div class="card success">
                    <h3><i class="fas fa-bell"></i> اختبار الإشعارات</h3>
                    <p>اختبار الإشعارات في الوضع المظلم والفاتح</p>
                    <span class="status-badge status-fixed">✅ مُصلح</span>
                    <div style="margin-top: 15px;">
                        <a href="test-notifications-dark-mode.html" class="btn success">فتح</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- التوثيق والأدلة -->
        <div class="section">
            <h2><i class="fas fa-book"></i> التوثيق والأدلة</h2>
            
            <div class="grid">
                <div class="card success">
                    <h3><i class="fas fa-book-open"></i> دليل الاستخدام التفاعلي</h3>
                    <p>دليل شامل وتفاعلي لاستخدام لوحة التحكم</p>
                    <span class="status-badge status-new">🆕 جديد</span>
                    <div style="margin-top: 15px;">
                        <a href="src/remote/admin-guide.html" class="btn success">فتح</a>
                    </div>
                </div>
                
                <div class="card info">
                    <h3><i class="fas fa-bookmark"></i> المرجع السريع</h3>
                    <p>مرجع سريع تفاعلي لجميع الميزات</p>
                    <span class="status-badge status-new">🆕 جديد</span>
                    <div style="margin-top: 15px;">
                        <a href="QUICK_REFERENCE.html" class="btn info">فتح</a>
                    </div>
                </div>
                
                <div class="card warning">
                    <h3><i class="fas fa-chart-bar"></i> نتائج الاختبار</h3>
                    <p>تقرير شامل لنتائج جميع الاختبارات</p>
                    <span class="status-badge status-working">✅ 100%</span>
                    <div style="margin-top: 15px;">
                        <a href="test-results.html" class="btn warning">فتح</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- معلومات المشروع -->
        <div class="section">
            <h2><i class="fas fa-info-circle"></i> معلومات المشروع</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                    <h4><i class="fas fa-check-circle"></i> الإنجازات</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li>✅ إصلاح إضافة الزبائن</li>
                        <li>✅ تطوير لوحة التحكم</li>
                        <li>✅ إصلاح الإشعارات</li>
                        <li>✅ إصلاح أقسام التنقل</li>
                        <li>✅ توثيق شامل</li>
                    </ul>
                </div>
                
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                    <h4><i class="fas fa-code"></i> التقنيات</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li>• HTML5 & CSS3</li>
                        <li>• JavaScript (ES6+)</li>
                        <li>• Font Awesome Icons</li>
                        <li>• Responsive Design</li>
                        <li>• Local Storage</li>
                    </ul>
                </div>
                
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                    <h4><i class="fas fa-headset"></i> الدعم الفني</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li>📞 الهاتف: 0696924176</li>
                        <li>💬 واتساب: 0696924176</li>
                        <li>🕐 أوقات العمل: 8:00 ص - 8:00 م</li>
                        <li>🛠️ دعم فني 24/7</li>
                    </ul>
                </div>
                
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                    <h4><i class="fas fa-shield-alt"></i> الحالة</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li>📦 الإصدار: 2.3.0</li>
                        <li>✅ الحالة: مكتمل 100%</li>
                        <li>📅 آخر تحديث: ديسمبر 2024</li>
                        <li>🟢 جميع الأنظمة تعمل</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- روابط سريعة -->
        <div class="section" style="text-align: center;">
            <h2><i class="fas fa-external-link-alt"></i> روابط سريعة</h2>
            
            <div style="margin-top: 30px;">
                <a href="index.html" class="btn success">
                    <i class="fas fa-home"></i> التطبيق الرئيسي
                </a>
                <a href="src/remote/admin-panel-fixed.html" class="btn info">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a>
                <a href="src/remote/admin-guide.html" class="btn warning">
                    <i class="fas fa-book-open"></i> دليل الاستخدام
                </a>
                <a href="src/remote/test-admin-panel.html" class="btn danger">
                    <i class="fas fa-vial"></i> صفحة الاختبار
                </a>
                <a href="QUICK_REFERENCE.html" class="btn">
                    <i class="fas fa-bookmark"></i> المرجع السريع
                </a>
            </div>
            
            <div style="margin-top: 40px; padding: 30px; background: rgba(255,255,255,0.8); border-radius: 15px;">
                <h3 style="color: #28a745; margin-bottom: 15px;">
                    🎉 تم إنجاز المشروع بنجاح!
                </h3>
                <p style="color: #666; font-size: 1.1rem;">
                    جميع المشاكل تم حلها، والميزات الجديدة تم تطويرها، والنظام جاهز للاستخدام الكامل
                </p>
            </div>
        </div>
    </div>

    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', () => {
            // تأثير الظهور التدريجي
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);
            
            document.querySelectorAll('.section').forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(section);
            });
            
            // تحديث الوقت
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-DZ');
                
                // إضافة الوقت إلى الرأس إذا لم يكن موجوداً
                if (!document.getElementById('current-time')) {
                    const timeDiv = document.createElement('div');
                    timeDiv.id = 'current-time';
                    timeDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; background: rgba(255,255,255,0.9); padding: 10px 15px; border-radius: 25px; font-size: 14px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); z-index: 1000;';
                    timeDiv.innerHTML = `<i class="fas fa-clock"></i> ${timeString}`;
                    document.body.appendChild(timeDiv);
                }
            }
            
            updateTime();
            setInterval(updateTime, 1000);
        });
    </script>
</body>
</html>
