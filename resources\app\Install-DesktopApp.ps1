# PowerShell script لتثبيت مؤسسة وقود المستقبل على سطح المكتب
# Install Future Fuel Corporation app to desktop

param(
    [switch]$CreateShortcutOnly,
    [switch]$FullInstall,
    [switch]$Silent,
    [string]$InstallPath = "$env:PROGRAMFILES\مؤسسة وقود المستقبل"
)

# إعداد الترميز للعربية
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    تثبيت مؤسسة وقود المستقبل" -ForegroundColor Yellow
Write-Host "    Future Fuel Corporation" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من الصلاحيات
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# إنشاء اختصار
function 
 {
    param(
        [string]$TargetPath,
        [string]$ShortcutPath,
        [string]$WorkingDirectory,
        [string]$IconLocation,
        [string]$Description
    )
    
    try {
        $WshShell = New-Object -ComObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
        $Shortcut.TargetPath = $TargetPath
        $Shortcut.WorkingDirectory = $WorkingDirectory
        $Shortcut.Description = $Description
        if ($IconLocation -and (Test-Path $IconLocation)) {
            $Shortcut.IconLocation = $IconLocation
        }
        $Shortcut.Save()
        return $true
    }
    catch {
        Write-Host "❌ خطأ في إنشاء الاختصار: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# الحصول على مسار سطح المكتب
$DesktopPath = [Environment]::GetFolderPath("Desktop")
$StartMenuPath = [Environment]::GetFolderPath("StartMenu") + "\Programs"

Write-Host "📁 مسار سطح المكتب: $DesktopPath" -ForegroundColor Blue
Write-Host "📂 مسار التطبيق: $(Get-Location)" -ForegroundColor Blue
Write-Host ""

# التحقق من وجود الملفات المطلوبة
$RequiredFiles = @("run-app.bat", "package.json")
$MissingFiles = @()

foreach ($file in $RequiredFiles) {
    if (-not (Test-Path $file)) {
        $MissingFiles += $file
    }
}

if ($MissingFiles.Count -gt 0) {
    Write-Host "❌ الملفات التالية مفقودة:" -ForegroundColor Red
    $MissingFiles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Red }
    Write-Host "يرجى التأكد من وجودك في مجلد التطبيق الصحيح" -ForegroundColor Yellow
    exit 1
}

# قراءة معلومات التطبيق من package.json
try {
    $PackageInfo = Get-Content "package.json" | ConvertFrom-Json
    $AppName = $PackageInfo.productName
    $AppVersion = $PackageInfo.version
    $AppDescription = $PackageInfo.description
    
    Write-Host "✅ معلومات التطبيق:" -ForegroundColor Green
    Write-Host "   الاسم: $AppName" -ForegroundColor White
    Write-Host "   الإصدار: $AppVersion" -ForegroundColor White
    Write-Host "   الوصف: $AppDescription" -ForegroundColor White
    Write-Host ""
}
catch {
    Write-Host "⚠️  تعذر قراءة معلومات التطبيق من package.json" -ForegroundColor Yellow
    $AppName = "مؤسسة وقود المستقبل"
    $AppDescription = "نظام إدارة مؤسسة وقود المستقبل"
}

# تحديد مسار الأيقونة
$IconPath = ""
$PossibleIconPaths = @(
    "icons\app-icon.ico",
    "assets\icons\app-icon.ico",
    "app-icon.ico"
)

foreach ($path in $PossibleIconPaths) {
    if (Test-Path $path) {
        $IconPath = (Resolve-Path $path).Path
        Write-Host "✅ تم العثور على الأيقونة: $IconPath" -ForegroundColor Green
        break
    }
}

if (-not $IconPath) {
    Write-Host "⚠️  لم يتم العثور على ملف الأيقونة، سيتم استخدام الأيقونة الافتراضية" -ForegroundColor Yellow
}

# إنشاء اختصار سطح المكتب
Write-Host "🔧 إنشاء اختصار سطح المكتب..." -ForegroundColor Yellow

$DesktopShortcut = "$DesktopPath\$AppName.lnk"
$CurrentPath = (Get-Location).Path
$RunAppPath = "$CurrentPath\run-app.bat"

if (Create-Shortcut -TargetPath $RunAppPath -ShortcutPath $DesktopShortcut -WorkingDirectory $CurrentPath -IconLocation $IconPath -Description $AppDescription) {
    Write-Host "✅ تم إنشاء اختصار سطح المكتب بنجاح!" -ForegroundColor Green
} else {
    Write-Host "❌ فشل في إنشاء اختصار سطح المكتب" -ForegroundColor Red
}

# إنشاء اختصار قائمة ابدأ
if (-not $Silent) {
    $CreateStartMenu = Read-Host "هل تريد إنشاء اختصار في قائمة ابدأ؟ (y/n)"
    if ($CreateStartMenu -eq "y" -or $CreateStartMenu -eq "Y") {
        Write-Host "🔧 إنشاء اختصار قائمة ابدأ..." -ForegroundColor Yellow
        
        $StartMenuShortcut = "$StartMenuPath\$AppName.lnk"
        
        if (Create-Shortcut -TargetPath $RunAppPath -ShortcutPath $StartMenuShortcut -WorkingDirectory $CurrentPath -IconLocation $IconPath -Description $AppDescription) {
            Write-Host "✅ تم إنشاء اختصار قائمة ابدأ بنجاح!" -ForegroundColor Green
        } else {
            Write-Host "❌ فشل في إنشاء اختصار قائمة ابدأ" -ForegroundColor Red
        }
    }
}

# تثبيت كامل (اختياري)
if ($FullInstall -and -not $CreateShortcutOnly) {
    if (Test-Administrator) {
        Write-Host "🚀 بدء التثبيت الكامل..." -ForegroundColor Yellow
        
        # إنشاء مجلد التثبيت
        if (-not (Test-Path $InstallPath)) {
            New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
            Write-Host "✅ تم إنشاء مجلد التثبيت: $InstallPath" -ForegroundColor Green
        }
        
        # نسخ الملفات
        Write-Host "📁 نسخ ملفات التطبيق..." -ForegroundColor Yellow
        Copy-Item -Path "$CurrentPath\*" -Destination $InstallPath -Recurse -Force
        Write-Host "✅ تم نسخ الملفات بنجاح" -ForegroundColor Green
        
        # إنشاء اختصارات تشير للمجلد الجديد
        $InstalledRunApp = "$InstallPath\run-app.bat"
        $InstalledIcon = "$InstallPath\icons\app-icon.ico"
        
        if (Test-Path $InstalledIcon) {
            $IconPath = $InstalledIcon
        }
        
        # تحديث اختصار سطح المكتب
        if (Create-Shortcut -TargetPath $InstalledRunApp -ShortcutPath $DesktopShortcut -WorkingDirectory $InstallPath -IconLocation $IconPath -Description $AppDescription) {
            Write-Host "✅ تم تحديث اختصار سطح المكتب" -ForegroundColor Green
        }
        
        Write-Host "✅ تم التثبيت الكامل بنجاح!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  التثبيت الكامل يتطلب صلاحيات المدير" -ForegroundColor Yellow
        Write-Host "يرجى تشغيل PowerShell كمدير وإعادة المحاولة" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "تم الانتهاء من التثبيت!" -ForegroundColor Green
Write-Host ""
Write-Host "يمكنك الآن:" -ForegroundColor White
Write-Host "• النقر المزدوج على أيقونة سطح المكتب" -ForegroundColor White
Write-Host "• البحث عن '$AppName' في قائمة ابدأ" -ForegroundColor White
Write-Host "• تشغيل التطبيق من مجلد التطبيق" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan

if (-not $Silent) {
    $RunNow = Read-Host "هل تريد تشغيل التطبيق الآن؟ (y/n)"
    if ($RunNow -eq "y" -or $RunNow -eq "Y") {
        Write-Host "🚀 تشغيل التطبيق..." -ForegroundColor Yellow
        Start-Process -FilePath $RunAppPath -WorkingDirectory $CurrentPath
    }
}
