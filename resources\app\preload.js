const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// تعريض APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
    // حفظ البيانات
    saveData: (data) => {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataPath = path.join(__dirname, 'data.json');
            fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    },

    // تحميل البيانات
    loadData: () => {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataPath = path.join(__dirname, 'data.json');
            if (fs.existsSync(dataPath)) {
                const data = fs.readFileSync(dataPath, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            return null;
        }
    },

    // إنشاء نسخة احتياطية
    createBackup: () => {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataPath = path.join(__dirname, 'data.json');
            const backupPath = path.join(__dirname, `backup_${Date.now()}.json`);
            
            if (fs.existsSync(dataPath)) {
                fs.copyFileSync(dataPath, backupPath);
                return true;
            }
            return false;
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            return false;
        }
    },

    // معالجة مربعات الحوار
    showSaveDialog: (options) => ipcRenderer.invoke('save-dialog', options),
    showOpenDialog: (options) => ipcRenderer.invoke('open-dialog', options),

    // فتح روابط خارجية
    openExternal: (url) => ipcRenderer.invoke('open-external', url),

    // معلومات النظام
    getSystemInfo: () => ipcRenderer.invoke('get-system-info'),

    // إدارة الثيم
    setTheme: (theme) => ipcRenderer.invoke('set-theme', theme),
    getTheme: () => ipcRenderer.invoke('get-theme'),

    // إشعارات
    createAutoBackup: () => ipcRenderer.send('create-auto-backup'),

    // إشعارات النظام
    showNotification: (title, body, icon) => {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, { body, icon });
        }
    },

    // طلب إذن الإشعارات
    requestNotificationPermission: async () => {
        if ('Notification' in window) {
            return await Notification.requestPermission();
        }
        return 'denied';
    },

    // إدارة تسجيل الدخول
    login: (credentials) => ipcRenderer.invoke('login', credentials),
    logout: () => ipcRenderer.invoke('logout'),
    reloadAfterLogin: () => ipcRenderer.invoke('reload-after-login'),

    // إدارة طلبات التفعيل
    submitActivationRequest: (requestData) => ipcRenderer.invoke('submit-activation-request', requestData),

    // إدارة البيانات المحسنة
    saveCustomerData: (customers) => {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataDir = path.join(__dirname, 'data');
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }
            const customersPath = path.join(dataDir, 'customers.json');
            fs.writeFileSync(customersPath, JSON.stringify(customers, null, 2));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ بيانات الزبائن:', error);
            return false;
        }
    },

    loadCustomerData: () => {
        try {
            const fs = require('fs');
            const path = require('path');
            const customersPath = path.join(__dirname, 'data', 'customers.json');
            if (fs.existsSync(customersPath)) {
                const data = fs.readFileSync(customersPath, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error('خطأ في تحميل بيانات الزبائن:', error);
            return [];
        }
    },

    // إدارة التراخيص
    saveLicenseData: (licenses) => {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataDir = path.join(__dirname, 'data');
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }
            const licensesPath = path.join(dataDir, 'licenses.json');
            fs.writeFileSync(licensesPath, JSON.stringify(licenses, null, 2));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ بيانات التراخيص:', error);
            return false;
        }
    },

    loadLicenseData: () => {
        try {
            const fs = require('fs');
            const path = require('path');
            const licensesPath = path.join(__dirname, 'data', 'licenses.json');
            if (fs.existsSync(licensesPath)) {
                const data = fs.readFileSync(licensesPath, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error('خطأ في تحميل بيانات التراخيص:', error);
            return [];
        }
    },

    // إدارة الإحصائيات
    saveStatistics: (stats) => {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataDir = path.join(__dirname, 'data');
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }
            const statsPath = path.join(dataDir, 'statistics.json');
            fs.writeFileSync(statsPath, JSON.stringify(stats, null, 2));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإحصائيات:', error);
            return false;
        }
    },

    loadStatistics: () => {
        try {
            const fs = require('fs');
            const path = require('path');
            const statsPath = path.join(__dirname, 'data', 'statistics.json');
            if (fs.existsSync(statsPath)) {
                const data = fs.readFileSync(statsPath, 'utf8');
                return JSON.parse(data);
            }
            return {
                totalCustomers: 0,
                totalLicenses: 0,
                totalRevenue: 0,
                monthlyStats: []
            };
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
            return {
                totalCustomers: 0,
                totalLicenses: 0,
                totalRevenue: 0,
                monthlyStats: []
            };
        }
    },

    // تصدير البيانات
    exportData: (data, filename) => {
        try {
            const fs = require('fs');
            const path = require('path');
            const os = require('os');
            const exportPath = path.join(os.homedir(), 'Desktop', filename);
            fs.writeFileSync(exportPath, JSON.stringify(data, null, 2));
            return exportPath;
        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            return null;
        }
    },

    // فحص حالة الاتصال
    checkConnection: () => {
        return navigator.onLine;
    },

    // إدارة الإعدادات
    saveSettings: (settings) => {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataDir = path.join(__dirname, 'data');
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }
            const settingsPath = path.join(dataDir, 'settings.json');
            fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    },

    loadSettings: () => {
        try {
            const fs = require('fs');
            const path = require('path');
            const settingsPath = path.join(__dirname, 'data', 'settings.json');
            if (fs.existsSync(settingsPath)) {
                const data = fs.readFileSync(settingsPath, 'utf8');
                return JSON.parse(data);
            }
            return {
                theme: 'light',
                language: 'ar',
                notifications: true,
                autoBackup: true
            };
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
            return {
                theme: 'light',
                language: 'ar',
                notifications: true,
                autoBackup: true
            };
        }
    }
});
