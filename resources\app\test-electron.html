<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Electron - CFGPLProgram</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: var(--card-bg-color, white);
            border-radius: 15px;
            box-shadow: var(--box-shadow, 0 4px 15px rgba(0,0,0,0.1));
        }
        
        .test-section {
            background: var(--bg-color, #f8f9fa);
            border: 1px solid var(--border-color, #dee2e6);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            transition: all 0.3s ease;
        }
        
        .test-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .dark-mode .test-item {
            background: var(--surface-color, #2d2d2d);
            border-color: #3498db;
        }
        
        .test-item.success {
            border-left-color: #27ae60;
        }
        
        .test-item.warning {
            border-left-color: #f39c12;
        }
        
        .test-item.danger {
            border-left-color: #e74c3c;
        }
        
        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .status-pass {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            border: 1px solid rgba(39, 174, 96, 0.3);
        }
        
        .status-fail {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }
        
        .status-pending {
            background: rgba(243, 156, 18, 0.1);
            color: #f39c12;
            border: 1px solid rgba(243, 156, 18, 0.3);
        }
        
        .test-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .test-btn.success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }
        
        .test-btn.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .test-btn.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .console-output {
            background: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .electron-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--text-color, #333); margin-bottom: 30px;">
            ⚡ اختبار Electron - CFGPLProgram
        </h1>
        
        <!-- معلومات Electron -->
        <div class="electron-info" id="electron-info">
            <h3><i class="fas fa-desktop"></i> معلومات Electron</h3>
            <div id="electron-details">
                جاري فحص بيئة Electron...
            </div>
        </div>
        
        <!-- اختبار APIs -->
        <div class="test-section">
            <h3>🔌 اختبار APIs الأساسية</h3>
            
            <div class="test-item" id="electron-api-test">
                <span>فحص وجود electronAPI</span>
                <div>
                    <span class="status-badge status-pending" id="electron-api-status">في الانتظار</span>
                    <button class="test-btn" onclick="testElectronAPI()">اختبار</button>
                </div>
            </div>
            
            <div class="test-item" id="preload-test">
                <span>فحص preload script</span>
                <div>
                    <span class="status-badge status-pending" id="preload-status">في الانتظار</span>
                    <button class="test-btn" onclick="testPreload()">اختبار</button>
                </div>
            </div>
            
            <div class="test-item" id="ipc-test">
                <span>فحص IPC communication</span>
                <div>
                    <span class="status-badge status-pending" id="ipc-status">في الانتظار</span>
                    <button class="test-btn" onclick="testIPC()">اختبار</button>
                </div>
            </div>
        </div>
        
        <!-- اختبار الإشعارات -->
        <div class="test-section">
            <h3>🔔 اختبار الإشعارات</h3>
            
            <div class="test-item">
                <span>إشعار نجاح</span>
                <button class="test-btn success" onclick="testNotification('success')">اختبار</button>
            </div>
            
            <div class="test-item">
                <span>إشعار خطأ</span>
                <button class="test-btn danger" onclick="testNotification('error')">اختبار</button>
            </div>
            
            <div class="test-item">
                <span>إشعار تحذير</span>
                <button class="test-btn warning" onclick="testNotification('warning')">اختبار</button>
            </div>
            
            <div class="test-item">
                <span>إشعار معلومات</span>
                <button class="test-btn" onclick="testNotification('info')">اختبار</button>
            </div>
        </div>
        
        <!-- اختبار إدارة البيانات -->
        <div class="test-section">
            <h3>💾 اختبار إدارة البيانات</h3>
            
            <div class="test-item" id="save-data-test">
                <span>حفظ البيانات</span>
                <div>
                    <span class="status-badge status-pending" id="save-data-status">في الانتظار</span>
                    <button class="test-btn" onclick="testSaveData()">اختبار</button>
                </div>
            </div>
            
            <div class="test-item" id="load-data-test">
                <span>تحميل البيانات</span>
                <div>
                    <span class="status-badge status-pending" id="load-data-status">في الانتظار</span>
                    <button class="test-btn" onclick="testLoadData()">اختبار</button>
                </div>
            </div>
            
            <div class="test-item" id="backup-test">
                <span>إنشاء نسخة احتياطية</span>
                <div>
                    <span class="status-badge status-pending" id="backup-status">في الانتظار</span>
                    <button class="test-btn" onclick="testBackup()">اختبار</button>
                </div>
            </div>
        </div>
        
        <!-- اختبار الوضع المظلم -->
        <div class="test-section">
            <h3>🌙 اختبار الوضع المظلم</h3>
            
            <div class="test-item">
                <span>تبديل إلى الوضع المظلم</span>
                <button class="test-btn" onclick="testDarkMode(true)">تفعيل</button>
            </div>
            
            <div class="test-item">
                <span>تبديل إلى الوضع الفاتح</span>
                <button class="test-btn" onclick="testDarkMode(false)">تفعيل</button>
            </div>
        </div>
        
        <!-- اختبار الجداول والإحصائيات -->
        <div class="test-section">
            <h3>📊 اختبار الجداول والإحصائيات</h3>
            
            <div class="test-item" id="table-test">
                <span>تحديث جدول الزبائن</span>
                <div>
                    <span class="status-badge status-pending" id="table-status">في الانتظار</span>
                    <button class="test-btn" onclick="testTable()">اختبار</button>
                </div>
            </div>
            
            <div class="test-item" id="stats-test">
                <span>تحديث الإحصائيات</span>
                <div>
                    <span class="status-badge status-pending" id="stats-status">في الانتظار</span>
                    <button class="test-btn" onclick="testStats()">اختبار</button>
                </div>
            </div>
        </div>
        
        <!-- مخرجات وحدة التحكم -->
        <div class="test-section">
            <h3>📋 مخرجات وحدة التحكم</h3>
            <div class="console-output" id="console-output">
                انقر على "بدء المراقبة" لعرض رسائل وحدة التحكم...
            </div>
            <button class="test-btn" onclick="startConsoleMonitoring()">بدء المراقبة</button>
            <button class="test-btn" onclick="clearConsole()">مسح</button>
        </div>
        
        <!-- اختبار شامل -->
        <div class="test-section">
            <h3>🚀 اختبار شامل</h3>
            <div style="text-align: center;">
                <button class="test-btn success" onclick="runAllTests()" style="padding: 15px 30px; font-size: 16px;">
                    تشغيل جميع الاختبارات
                </button>
                
                <button class="test-btn" onclick="openMainApp()" style="padding: 15px 30px; font-size: 16px; margin-right: 10px;">
                    فتح التطبيق الرئيسي
                </button>
            </div>
        </div>
    </div>

    <script src="scripts/electron-fixes.js"></script>
    <script>
        let consoleOutput = document.getElementById('console-output');
        let originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'warn' ? '#feca57' : '#48dbfb';
            consoleOutput.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function startConsoleMonitoring() {
            console.log = function(...args) {
                originalConsole.log.apply(console, args);
                addToConsole(args.join(' '), 'log');
            };
            
            console.error = function(...args) {
                originalConsole.error.apply(console, args);
                addToConsole(args.join(' '), 'error');
            };
            
            console.warn = function(...args) {
                originalConsole.warn.apply(console, args);
                addToConsole(args.join(' '), 'warn');
            };
            
            addToConsole('🔍 بدء مراقبة وحدة التحكم...', 'log');
        }

        function clearConsole() {
            consoleOutput.innerHTML = '';
        }

        function setTestStatus(testId, status) {
            const statusElement = document.getElementById(testId + '-status');
            if (statusElement) {
                statusElement.className = `status-badge status-${status}`;
                statusElement.textContent = status === 'pass' ? '✅ نجح' : status === 'fail' ? '❌ فشل' : '⏳ جاري...';
            }
        }

        function testElectronAPI() {
            setTestStatus('electron-api', 'pending');
            
            setTimeout(() => {
                if (typeof window.electronAPI !== 'undefined') {
                    setTestStatus('electron-api', 'pass');
                    addToConsole('✅ electronAPI متاح');
                } else {
                    setTestStatus('electron-api', 'fail');
                    addToConsole('❌ electronAPI غير متاح');
                }
            }, 500);
        }

        function testPreload() {
            setTestStatus('preload', 'pending');
            
            setTimeout(() => {
                if (window.electronAPI && typeof window.electronAPI.saveData === 'function') {
                    setTestStatus('preload', 'pass');
                    addToConsole('✅ preload script يعمل بشكل صحيح');
                } else {
                    setTestStatus('preload', 'fail');
                    addToConsole('❌ preload script لا يعمل');
                }
            }, 500);
        }

        function testIPC() {
            setTestStatus('ipc', 'pending');
            
            setTimeout(() => {
                if (window.electronAPI && typeof window.electronAPI.getSystemInfo === 'function') {
                    window.electronAPI.getSystemInfo().then(info => {
                        setTestStatus('ipc', 'pass');
                        addToConsole(`✅ IPC يعمل - النظام: ${info.platform}`);
                    }).catch(() => {
                        setTestStatus('ipc', 'fail');
                        addToConsole('❌ IPC لا يعمل');
                    });
                } else {
                    setTestStatus('ipc', 'fail');
                    addToConsole('❌ IPC غير متاح');
                }
            }, 500);
        }

        function testNotification(type) {
            const messages = {
                success: 'اختبار إشعار النجاح في Electron',
                error: 'اختبار إشعار الخطأ في Electron',
                warning: 'اختبار إشعار التحذير في Electron',
                info: 'اختبار إشعار المعلومات في Electron'
            };
            
            if (typeof showToast === 'function') {
                showToast(messages[type], type);
                addToConsole(`🔔 تم إرسال إشعار ${type}`);
            } else {
                addToConsole(`❌ دالة showToast غير متاحة`);
            }
        }

        function testSaveData() {
            setTestStatus('save-data', 'pending');
            
            const testData = {
                test: true,
                timestamp: new Date().toISOString(),
                message: 'اختبار حفظ البيانات في Electron'
            };
            
            setTimeout(() => {
                if (window.electronAPI && typeof window.electronAPI.saveData === 'function') {
                    const result = window.electronAPI.saveData(testData);
                    if (result) {
                        setTestStatus('save-data', 'pass');
                        addToConsole('✅ تم حفظ البيانات بنجاح');
                    } else {
                        setTestStatus('save-data', 'fail');
                        addToConsole('❌ فشل في حفظ البيانات');
                    }
                } else {
                    setTestStatus('save-data', 'fail');
                    addToConsole('❌ دالة حفظ البيانات غير متاحة');
                }
            }, 500);
        }

        function testLoadData() {
            setTestStatus('load-data', 'pending');
            
            setTimeout(() => {
                if (window.electronAPI && typeof window.electronAPI.loadData === 'function') {
                    const data = window.electronAPI.loadData();
                    if (data) {
                        setTestStatus('load-data', 'pass');
                        addToConsole('✅ تم تحميل البيانات بنجاح');
                    } else {
                        setTestStatus('load-data', 'pass');
                        addToConsole('✅ لا توجد بيانات محفوظة (طبيعي)');
                    }
                } else {
                    setTestStatus('load-data', 'fail');
                    addToConsole('❌ دالة تحميل البيانات غير متاحة');
                }
            }, 500);
        }

        function testBackup() {
            setTestStatus('backup', 'pending');
            
            setTimeout(() => {
                if (window.electronAPI && typeof window.electronAPI.createBackup === 'function') {
                    const result = window.electronAPI.createBackup();
                    if (result) {
                        setTestStatus('backup', 'pass');
                        addToConsole('✅ تم إنشاء النسخة الاحتياطية');
                    } else {
                        setTestStatus('backup', 'pass');
                        addToConsole('✅ لا توجد بيانات للنسخ الاحتياطي');
                    }
                } else {
                    setTestStatus('backup', 'fail');
                    addToConsole('❌ دالة النسخ الاحتياطي غير متاحة');
                }
            }, 500);
        }

        function testDarkMode(enable) {
            if (typeof toggleDarkMode === 'function') {
                const currentMode = document.body.classList.contains('dark-mode');
                if ((enable && !currentMode) || (!enable && currentMode)) {
                    toggleDarkMode();
                }
                addToConsole(`🌙 تم تبديل الوضع إلى ${enable ? 'المظلم' : 'الفاتح'}`);
            } else {
                addToConsole('❌ دالة تبديل الوضع المظلم غير متاحة');
            }
        }

        function testTable() {
            setTestStatus('table', 'pending');
            
            setTimeout(() => {
                if (typeof updateCustomersTable === 'function') {
                    updateCustomersTable();
                    setTestStatus('table', 'pass');
                    addToConsole('✅ تم تحديث جدول الزبائن');
                } else {
                    setTestStatus('table', 'fail');
                    addToConsole('❌ دالة تحديث الجدول غير متاحة');
                }
            }, 500);
        }

        function testStats() {
            setTestStatus('stats', 'pending');
            
            setTimeout(() => {
                if (typeof updateStatistics === 'function') {
                    updateStatistics();
                    setTestStatus('stats', 'pass');
                    addToConsole('✅ تم تحديث الإحصائيات');
                } else {
                    setTestStatus('stats', 'fail');
                    addToConsole('❌ دالة تحديث الإحصائيات غير متاحة');
                }
            }, 500);
        }

        function runAllTests() {
            addToConsole('🚀 بدء الاختبار الشامل...');
            
            setTimeout(() => testElectronAPI(), 500);
            setTimeout(() => testPreload(), 1000);
            setTimeout(() => testIPC(), 1500);
            setTimeout(() => testSaveData(), 2000);
            setTimeout(() => testLoadData(), 2500);
            setTimeout(() => testBackup(), 3000);
            setTimeout(() => testTable(), 3500);
            setTimeout(() => testStats(), 4000);
            
            setTimeout(() => {
                addToConsole('✅ انتهى الاختبار الشامل');
            }, 5000);
        }

        function openMainApp() {
            window.location.href = 'index.html';
        }

        function updateElectronInfo() {
            const infoDiv = document.getElementById('electron-details');
            
            if (typeof window.electronAPI !== 'undefined') {
                window.electronAPI.getSystemInfo().then(info => {
                    infoDiv.innerHTML = `
                        <p><strong>البيئة:</strong> Electron ✅</p>
                        <p><strong>النظام:</strong> ${info.platform}</p>
                        <p><strong>المعمارية:</strong> ${info.arch}</p>
                        <p><strong>إصدار التطبيق:</strong> ${info.version}</p>
                        <p><strong>إصدار Electron:</strong> ${info.electronVersion}</p>
                        <p><strong>إصدار Node.js:</strong> ${info.nodeVersion}</p>
                    `;
                }).catch(() => {
                    infoDiv.innerHTML = '<p><strong>البيئة:</strong> Electron (معلومات محدودة)</p>';
                });
            } else {
                infoDiv.innerHTML = '<p><strong>البيئة:</strong> متصفح ويب عادي</p>';
            }
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            addToConsole('📋 صفحة اختبار Electron جاهزة');
            startConsoleMonitoring();
            updateElectronInfo();
            
            setTimeout(() => {
                addToConsole('🔍 بدء الفحص التلقائي...');
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
