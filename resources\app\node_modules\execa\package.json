{"name": "execa", "version": "7.2.0", "description": "Process execution for humans", "license": "MIT", "repository": "sindresorhus/execa", "funding": "https://github.com/sindresorhus/execa?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "scripts": {"test": "xo && c8 ava && tsd"}, "files": ["index.js", "index.d.ts", "lib"], "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.1", "human-signals": "^4.3.0", "is-stream": "^3.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "onetime": "^6.0.0", "signal-exit": "^3.0.7", "strip-final-newline": "^3.0.0"}, "devDependencies": {"@types/node": "^18.13.0", "ava": "^5.2.0", "c8": "^7.12.0", "get-node": "^13.5.0", "is-running": "^2.1.0", "p-event": "^5.0.1", "path-key": "^4.0.0", "tempfile": "^4.0.0", "tsd": "^0.25.0", "xo": "^0.54.2"}, "c8": {"reporter": ["text", "lcov"], "exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "ava": {"workerThreads": false}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}}