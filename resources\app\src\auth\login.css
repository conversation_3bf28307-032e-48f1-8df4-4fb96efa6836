/* أنماط تسجيل الدخول المميزة */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --shadow-light: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 15px 40px rgba(0, 0, 0, 0.2);
    --shadow-heavy: 0 20px 50px rgba(0, 0, 0, 0.3);
    --border-radius: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    overflow: hidden;
    height: 100vh;
    background: var(--gradient-primary);
}

/* الخلفية المتحركة */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--gradient-primary);
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 15%;
    animation-delay: 1s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 2s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
}

.shape-5 {
    width: 140px;
    height: 140px;
    top: 50%;
    left: 50%;
    animation-delay: 4s;
    transform: translate(-50%, -50%);
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* حاوي تسجيل الدخول */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

/* بطاقة تسجيل الدخول */
.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    padding: 40px;
    width: 100%;
    max-width: 450px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* هيدر تسجيل الدخول */
.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo-container {
    margin-bottom: 20px;
}

.logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    box-shadow: var(--shadow-medium);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.login-header h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.subtitle {
    color: var(--secondary-color);
    font-size: 1rem;
    margin-bottom: 10px;
}

.version-info {
    background: var(--gradient-secondary);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-block;
    font-weight: 500;
}

/* حالة الترخيص */
.license-status {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #721c24;
    font-weight: 600;
}

.status-indicator i {
    font-size: 1.2rem;
    animation: blink 2s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* نموذج تسجيل الدخول */
.login-form {
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
}

.form-group input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    transform: translateY(-2px);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    font-size: 1.1rem;
    transition: var(--transition);
}

.toggle-password:hover {
    color: var(--secondary-color);
}

/* خيارات النموذج */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--primary-color);
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e1e8ed;
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
}

.forgot-password {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-password:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

/* زر تسجيل الدخول */
.login-btn {
    width: 100%;
    padding: 15px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.login-btn:active {
    transform: translateY(0);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* أزرار الإجراءات */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 25px;
}

.action-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.activation-btn {
    background: var(--gradient-success);
    color: white;
}

.contact-btn {
    background: var(--gradient-secondary);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

/* فوتر تسجيل الدخول */
.login-footer {
    text-align: center;
    border-top: 1px solid #e1e8ed;
    padding-top: 20px;
}

.system-info {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 0.8rem;
    color: #7f8c8d;
}

.separator {
    color: #bdc3c7;
}

.copyright {
    font-size: 0.8rem;
    color: #95a5a6;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}

.modal.show {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px 20px;
    border-bottom: 1px solid #e1e8ed;
}

.modal-header h2 {
    color: var(--primary-color);
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #7f8c8d;
    cursor: pointer;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: #f8f9fa;
    color: var(--accent-color);
}

/* نموذج التفعيل */
.activation-form {
    padding: 30px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.activation-form .form-group {
    margin-bottom: 20px;
}

.activation-form label {
    display: block;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.activation-form input,
.activation-form select,
.activation-form textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: var(--transition);
}

.activation-form input:focus,
.activation-form select:focus,
.activation-form textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

.submit-btn,
.cancel-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.submit-btn {
    background: var(--gradient-success);
    color: white;
}

.cancel-btn {
    background: #e9ecef;
    color: var(--primary-color);
}

.submit-btn:hover,
.cancel-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

/* معلومات المطور */
.contact-modal-content {
    max-width: 400px;
}

.developer-info {
    padding: 30px;
    text-align: center;
    border-bottom: 1px solid #e1e8ed;
}

.developer-avatar {
    margin-bottom: 20px;
}

.developer-avatar i {
    font-size: 4rem;
    color: var(--secondary-color);
}

.developer-details h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.title {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 10px;
}

.description {
    color: #7f8c8d;
    font-size: 0.9rem;
    line-height: 1.5;
}

.contact-methods {
    padding: 20px 30px;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 10px;
    text-decoration: none;
    transition: var(--transition);
    margin-bottom: 10px;
}

.contact-method.phone {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.contact-method.whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
}

.contact-method:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.contact-method i {
    font-size: 1.5rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
}

.label {
    font-size: 0.8rem;
    opacity: 0.9;
}

.value {
    font-size: 1.1rem;
    font-weight: 600;
}

.contact-note {
    padding: 20px 30px;
    background: #f8f9fa;
    border-top: 1px solid #e1e8ed;
    display: flex;
    align-items: center;
    gap: 10px;
}

.contact-note i {
    color: var(--secondary-color);
}

.contact-note p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

/* التجاوب */
@media (max-width: 768px) {
    .login-card {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .modal-content {
        margin: 10px;
        max-height: 95vh;
    }
}

/* أنماط حقل الترخيص */
.license-group {
    border: 2px dashed #007bff;
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    animation: pulse 2s infinite;
}

.license-group.show {
    display: block !important;
    animation: slideDown 0.3s ease-out;
}

.activate-license-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 0.9rem;
    cursor: pointer;
    margin-top: 8px;
    transition: all 0.3s ease;
    width: 100%;
}

.activate-license-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.activate-license-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.license-link {
    color: #007bff;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.license-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

@keyframes pulse {
    0% { border-color: #007bff; }
    50% { border-color: #28a745; }
    100% { border-color: #007bff; }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* أنماط الإشعارات مع دعم الوضع المظلم */
.toast {
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    border: 1px solid rgba(255,255,255,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.4;
}

.toast.success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border-color: rgba(46, 204, 113, 0.3);
}

.toast.error {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border-color: rgba(231, 76, 60, 0.3);
}

.toast.info {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-color: rgba(52, 152, 219, 0.3);
}

.toast.warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    border-color: rgba(243, 156, 18, 0.3);
}

/* الوضع المظلم للإشعارات */
.dark-mode .toast {
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    border: 1px solid rgba(255,255,255,0.1);
}

.dark-mode .toast.success {
    background: linear-gradient(135deg, #1e8449, #239b56);
    border-color: rgba(30, 132, 73, 0.5);
}

.dark-mode .toast.error {
    background: linear-gradient(135deg, #a93226, #cb4335);
    border-color: rgba(169, 50, 38, 0.5);
}

.dark-mode .toast.info {
    background: linear-gradient(135deg, #1f618d, #2874a6);
    border-color: rgba(31, 97, 141, 0.5);
}

.dark-mode .toast.warning {
    background: linear-gradient(135deg, #b7950b, #d68910);
    border-color: rgba(183, 149, 11, 0.5);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
