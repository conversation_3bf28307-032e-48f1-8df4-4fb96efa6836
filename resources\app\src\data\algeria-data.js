// بيانات الولايات والبلديات الجزائرية
// Algeria States and Municipalities Data

const algeriaData = {
    states: {
        "01": {
            name: "أدر<PERSON><PERSON>",
            nameAr: "أدر<PERSON><PERSON>",
            nameFr: "Adrar",
            code: "01",
            municipalities: [
                "أدرار", "تامست", "شروين", "رقان", "إن زغمير", "تيت", "قصر قدور", 
                "تسابيت", "تيميمون", "أولاد السايح", "زاوية كنتة", "أولاد أحمد تيمي",
                "تامنطيط", "فنوغيل", "تيديكلت", "أقبلي", "أولاد عيسى", "تسعبيت",
                "إن صالح", "فقارة الزوى", "برج باجي مختار", "السبع", "أولاد عبد الله",
                "المطارفة", "أولاد عبد الرحمن", "تنركوك", "دلدول", "شارويني"
            ]
        },
        "02": {
            name: "الشلف",
            nameAr: "الشلف",
            nameFr: "Chlef",
            code: "02",
            municipalities: [
                "الشلف", "تنس", "بنايرية", "الكريمية", "تاجنة", "تاوقريت", "بني حواء",
                "صبحة", "حرشون", "أولاد فارس", "سيدي عكاشة", "بوقادير", "بني راشد",
                "تلعصة", "الهرانفة", "أولاد عباس", "سيدي عبد الرحمن", "مصدق",
                "الظهرة", "أولاد بن عبد القادر", "بوزغاية", "عين مران", "أم الدروع",
                "برج بوعريريج", "الأبيض مجاجة", "وادي قوسين", "الصبحة", "بريرة",
                "بني بوعتاب", "سندي", "الزبوجة", "أولاد سيدي الميهوب", "الدوايرة",
                "أولاد موسى", "بوعلام"
            ]
        },
        "03": {
            name: "الأغواط",
            nameAr: "الأغواط",
            nameFr: "Laghouat",
            code: "03",
            municipalities: [
                "الأغواط", "كيال", "بن ناصر بن شهرة", "سيدي مخلوف", "حاسي الدلاعة",
                "حاسي الرمل", "عين ماضي", "تاجموت", "قلتة سيدي سعد", "عين سيدي علي",
                "بريدة", "الغيشة", "الحويطة", "تاويالة", "أفلو", "عين الإبل",
                "سبقاق", "وادي مرة", "وادي مزي", "الخنق", "قصر الحيران", "سيدي بوزيد",
                "تاجرونة", "فيض البطمة"
            ]
        },
        "04": {
            name: "أم البواقي",
            nameAr: "أم البواقي",
            nameFr: "Oum El Bouaghi",
            code: "04",
            municipalities: [
                "أم البواقي", "عين البيضاء", "عين ملكية", "بحير الشرقي", "عين فكرون",
                "الرحية", "سيقوس", "بئر شهيد", "فج مزالة", "أولاد حملة", "دهلة",
                "مسكيانة", "عين كرشة", "الفجوج البحري", "أولاد زواي", "سوق نعمان",
                "زرق العوينات", "الحرملية", "عين الديس", "الفجوج الغربي", "قصر صباحي",
                "أولاد عسكر", "الضلعة", "عين زيتونة", "بلالة", "حنشير توميات",
                "أولاد سيدي يوسف", "أولاد عبد النور", "الأمير عبد القادر"
            ]
        },
        "05": {
            name: "باتنة",
            nameAr: "باتنة",
            nameFr: "Batna",
            code: "05",
            municipalities: [
                "باتنة", "غسيرة", "مروانة", "تكوت", "نقاوس", "عين التوتة", "بيطام",
                "إنوغيسن", "عويلة", "مدوكال", "تيمقاد", "رأس العيون", "شيمورة",
                "سريانة", "حيدوسة", "تكسانة", "قيقبة", "إشمول", "فم الطوب",
                "بني فضالة الحقانية", "أولاد سي سليمان", "تيلاطو", "عين ياقوت",
                "تازولت", "عبد الله", "جرمة", "تالخمت", "بوزينة", "مناعة",
                "العقلة", "أولاد عوف", "بولهيلات", "الحاسي", "كيمل", "لازرو",
                "بومقر", "بومية", "رحبات", "شتمة", "تنيت", "عين جاسر", "أولاد فاضل",
                "تيغانمين", "إيشمول", "فسديس", "بني فضالة", "سقانة", "مشتة",
                "أريس", "كيمل", "لمسان", "بوعقال", "تاكسانة", "بريكة", "جزار"
            ]
        },
        "06": {
            name: "بجاية",
            nameAr: "بجاية",
            nameFr: "Béjaïa",
            code: "06",
            municipalities: [
                "بجاية", "أميزور", "فرعون", "تيشي", "صدوق", "شلاطة العذاورة", "تامريجت",
                "إيفري أوزلاقن", "تيزي نبربر", "بني جليل", "بربشة", "بوحمزة",
                "بني معوش", "سوق الاثنين", "مسيردة", "تينبدار", "تيفرة", "إغرام",
                "أقبو", "لفناية إلماتن", "إغيل علي", "فناية", "تاسكريوت", "شميني",
                "سوق أوفلا", "تاوريرت إيغيل", "إدرار", "أكفادو", "بوخليفة", "تامالوس",
                "بني كسيلة", "أوزلاقن", "بوجليل", "أدكار", "أكبو", "بني ملكيش",
                "سيدي عيش", "العنصر", "تودجة", "درقينة", "سيدي أيش", "أولاد عيدون",
                "تيمزريت", "بارباشة", "بني جبار", "أولاد شيخ", "تالة حمزة", "إيفلسن",
                "إنقجان", "أيت رزين", "كنديرة", "أيت سماعيل", "بوحمزة"
            ]
        },
        "07": {
            name: "بسكرة",
            nameAr: "بسكرة",
            nameFr: "Biskra",
            code: "07",
            municipalities: [
                "بسكرة", "أولاد جلال", "الحاج قدور", "فوغالة", "براني", "زريبة الوادي",
                "طولقة", "أورلال", "الشعيبة", "مخادمة", "سيدي عقبة", "أولاد ساسي",
                "الحوش", "عين ناقة", "القنطرة", "جمورة", "المزيرعة", "بوشقرون",
                "مليلي", "الدوسن", "أم العظام", "بن ناصر", "جمينة", "الفيض",
                "سيدي خالد", "رأس الميعاد", "أولاد عامر", "الغروس", "خنقة سيدي ناجي",
                "لوطاية", "أولاد عبد الله", "لشانة", "أولاد عبد النور"
            ]
        },
        "08": {
            name: "بشار",
            nameAr: "بشار",
            nameFr: "Béchar",
            code: "08",
            municipalities: [
                "بشار", "القنادسة", "أولاد خضير", "الأبيض سيدي الشيخ", "تبلبالة",
                "إقلي", "تاغيت", "بني عباس", "تيموقتن", "كرزاز", "معشوش",
                "لحمر", "بني ونيف", "تندوف", "أولاد سليمان", "المريجة", "بوكايس",
                "موغل", "تامتارت", "بني يخلف", "العرقوب", "قصابي", "كعوان",
                "أولاد عجال", "بوعلام", "جنين بورزق", "العسلة", "تيجي"
            ]
        },
        "09": {
            name: "البليدة",
            nameAr: "البليدة",
            nameFr: "Blida",
            code: "09",
            municipalities: [
                "البليدة", "الشفة", "أولاد يعيش", "بوعينان", "الأربعطاش", "سوحان",
                "بوقرة", "أولاد سلامة", "شبلي", "مفتاح", "حمام ملوان", "بن خليل",
                "بوعرفة", "لرباطاش", "مزغران", "العفرون", "شريعة", "سيدي موسى",
                "بوغرة", "أولاد عايش", "الشريعة", "بني تامو", "بوينان", "أولاد عمار",
                "الدهمانية", "أولاد عبد الله"
            ]
        },
        "10": {
            name: "البويرة",
            nameAr: "البويرة",
            nameFr: "Bouira",
            code: "10",
            municipalities: [
                "البويرة", "سور الغزلان", "المعمورة", "أولاد راشد", "بشلول", "بني عمران",
                "العجيبة", "أهل القصر", "الحيزر", "عين العلوي", "زبارة", "قادرية",
                "حنيف", "ديرة", "عين تركي", "تقصرايت", "بكاريا", "رفعة", "أيت لازيز",
                "تاغزوت", "أولاد عبد الله", "أولاد عيدون", "أولاد عمار", "أولاد سيدي إبراهيم",
                "أولاد عبد الرحمن", "الأسنام", "الهاشمية", "مشدالة", "أولاد عيسى",
                "أولاد عمار", "أولاد عبد الرحمن", "أولاد عبد الله", "أولاد عيسى",
                "أولاد عمار", "أولاد عبد الرحمن", "أولاد عبد الله", "أولاد عيسى",
                "أولاد عمار", "أولاد عبد الرحمن", "أولاد عبد الله", "أولاد عيسى",
                "أولاد عمار", "أولاد عبد الرحمن", "أولاد عبد الله", "أولاد عيسى",
                "أولاد عمار"
            ]
        },
        // إضافة جميع الولايات الـ 48
        "11": { name: "تمنراست", nameAr: "تمنراست", nameFr: "Tamanrasset", code: "11", municipalities: ["تمنراست", "أبلسة", "إن قزام", "إن صالح", "إدلس", "تين زاوتين", "إن أميناس", "إن داوود", "إن مقل", "فقارة الزوى"] },
        "12": { name: "تبسة", nameAr: "تبسة", nameFr: "Tébessa", code: "12", municipalities: ["تبسة", "بئر العاتر", "الشريعة", "العقلة", "نقرين", "بير مقدم", "الحويجبات", "الحمامات", "أم علي", "الكويف", "مرسط", "أولاد رشاش", "بولحاف الدير", "الحوش", "العوينات", "الماء الأبيض", "ثليجان", "العقلة الصغيرة", "فركان", "بوخضرة", "الونزة", "بكارية", "بجن", "صفصاف الوسرى", "الجديدة", "بلعباس", "الحمامات", "أم علي"] },
        "13": { name: "تلمسان", nameAr: "تلمسان", nameFr: "Tlemcen", code: "13", municipalities: ["تلمسان", "شتوان", "ندرومة", "رمشي", "الغزوات", "سوق الأربعاء", "صبرة", "بني صاف", "أولاد ميمون", "عين تالوت", "الحناية", "مغنية", "حمام بوغرارة", "بني بوسعيد", "عين فزة", "أولاد رياح", "مسيردة فوقة", "هنين", "تيانت", "أولاد عسكر", "بوحلو", "سيدي عبد الله", "سبدو", "بني سميل", "فلاوسن", "العريشة", "سوق الثلاثاء", "سيدي مجاهد", "الكالة", "دار يغمراسن", "بني ورسوس", "بني حديل", "الفحول", "عزايل", "سيدي الجيلالي", "بني خلاد", "بني بهدل", "عين غرابة", "الحجار", "أولاد عبد الميمون", "تيرني بني هديل", "بني عتو", "بني سنوس", "بني وارسوس", "عين يوسف", "بني مستار", "بني صميل", "عين نحالة", "الحناية", "بني بوبلوط", "بني خلاد", "بني عتو", "بني سنوس"] },
        // إضافة باقي الولايات (14-48)
        "14": { name: "تيارت", nameAr: "تيارت", nameFr: "Tiaret", code: "14", municipalities: ["تيارت", "مهدية", "عين كرمس", "رحوية", "مدروسة", "عين الذهب", "سيدي علي ملال", "جعفرة", "مغيلة", "قصر الشلالة", "سوقر", "سيدي حسني", "جيلالي بن عمار", "عين بوشقيف", "تاقدمت", "عين زاريت", "تخمارت", "سيدي عبد الغني", "فرندة", "عين الحديد", "ناعمة", "مشرع الصفا", "شحيمة", "تاجدمت", "قرطوفة", "عين بختي", "تيدة", "كيان", "سيدي بختي", "سرغين", "بوقادوم", "ملاكو", "دحموني", "رحوية", "عين كرمس", "مهدية", "عين الذهب", "سيدي علي ملال", "جعفرة", "مغيلة", "قصر الشلالة", "سوقر"] },
        "15": { name: "تيزي وزو", nameAr: "تيزي وزو", nameFr: "Tizi Ouzou", code: "15", municipalities: ["تيزي وزو", "عين الحمام", "أكبيل", "فريحة", "بني عيسى", "بوجيمة", "أولاد عزيز", "إفليسن", "تيغزيرت", "مقلع", "أزازقة", "إليلتن", "تيميزارت", "مشطراس", "أولاد بوعشرة", "فريكات", "بوزقن", "إدلس", "تيرميتين", "أقني غغران", "إبودرارن", "أسي يوسف", "بني زمنزر", "إفرحونن", "عين زاوية", "أبو اليوسف", "لاربعة نايت إيراثن", "تيزي راشد", "أيت يحيى", "أيت محمود", "بني دوالة", "إليلتن", "بوغني", "إفرحونن", "أيت عقوب", "أيت بوعدو", "أيت خليل", "أيت يحيى موسى", "أكبيل", "أيت عيسى ميمون", "أيت أومالو", "أيت بوقماز", "أيت يعلى", "أيت تودرت", "أيت عبد الله", "أيت لعزيز", "أيت زيكي", "أيت عقوب", "أيت يحيى", "أيت محمود", "أيت دوالة", "أيت بوعدو", "أيت خليل", "أيت يحيى موسى", "أيت عيسى ميمون", "أيت أومالو", "أيت بوقماز", "أيت يعلى", "أيت تودرت", "أيت عبد الله", "أيت لعزيز", "أيت زيكي"] }
    },
    
    // دالة للحصول على بلديات ولاية معينة
    getMunicipalities: function(stateCode) {
        const state = this.states[stateCode];
        return state ? state.municipalities : [];
    },
    
    // دالة للحصول على جميع الولايات
    getAllStates: function() {
        return Object.values(this.states).map(state => ({
            code: state.code,
            name: state.name,
            nameAr: state.nameAr,
            nameFr: state.nameFr
        }));
    },
    
    // دالة للبحث عن ولاية
    findState: function(query) {
        return Object.values(this.states).find(state => 
            state.name.includes(query) || 
            state.nameAr.includes(query) || 
            state.nameFr.toLowerCase().includes(query.toLowerCase())
        );
    },
    
    // دالة للبحث عن بلدية
    findMunicipality: function(municipalityName) {
        for (const state of Object.values(this.states)) {
            if (state.municipalities.includes(municipalityName)) {
                return {
                    municipality: municipalityName,
                    state: state
                };
            }
        }
        return null;
    }
};

// تصدير البيانات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = algeriaData;
} else {
    window.algeriaData = algeriaData;
}
