<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار لوحة التحكم - CFGPLProgram</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-btn:hover {
            background: #0056b3;
        }
        
        .test-btn.success {
            background: #28a745;
        }
        
        .test-btn.danger {
            background: #dc3545;
        }
        
        .test-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .result.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .iframe-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
            🧪 اختبار لوحة التحكم
        </h1>
        
        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h3>⚡ اختبارات سريعة</h3>
            
            <button class="test-btn" onclick="testAdminPanelLoad()">
                اختبار تحميل لوحة التحكم
            </button>
            
            <button class="test-btn success" onclick="openAdminPanel()">
                فتح لوحة التحكم الأصلية
            </button>
            
            <button class="test-btn warning" onclick="openSimplePanel()">
                فتح لوحة التحكم المبسطة
            </button>
            
            <button class="test-btn danger" onclick="openDebugPanel()">
                فتح أداة التشخيص
            </button>
            
            <div id="test-results"></div>
        </div>
        
        <!-- اختبار مدمج -->
        <div class="test-section">
            <h3>🔧 اختبار مدمج</h3>
            <p>لوحة التحكم مدمجة أدناه للاختبار المباشر:</p>
            
            <div class="iframe-container">
                <iframe src="admin-panel.html" id="admin-panel-frame"></iframe>
            </div>
            
            <div style="text-align: center; margin-top: 15px;">
                <button class="test-btn" onclick="reloadFrame()">
                    🔄 إعادة تحميل
                </button>
                
                <button class="test-btn" onclick="testFrameNavigation()">
                    🧪 اختبار التنقل
                </button>
                
                <button class="test-btn" onclick="injectFix()">
                    🔧 حقن الإصلاح
                </button>
            </div>
        </div>
        
        <!-- تعليمات الإصلاح -->
        <div class="test-section">
            <h3>📋 تعليمات الإصلاح اليدوي</h3>
            
            <div style="background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <h4>إذا لم تعمل الأقسام:</h4>
                <ol style="text-align: right;">
                    <li>افتح لوحة التحكم في نافذة جديدة</li>
                    <li>اضغط F12 لفتح أدوات المطور</li>
                    <li>اذهب إلى تبويب Console</li>
                    <li>انسخ والصق الكود التالي:</li>
                </ol>
                
                <div style="background: #2d2d2d; color: #f8f8f2; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; direction: ltr; text-align: left; margin: 10px 0;">
// إصلاح فوري للتنقل<br>
document.querySelectorAll('.nav-link').forEach(link => {<br>
&nbsp;&nbsp;link.addEventListener('click', (e) => {<br>
&nbsp;&nbsp;&nbsp;&nbsp;e.preventDefault();<br>
&nbsp;&nbsp;&nbsp;&nbsp;const section = link.dataset.section;<br>
&nbsp;&nbsp;&nbsp;&nbsp;document.querySelectorAll('.content-section').forEach(s => s.classList.remove('active'));<br>
&nbsp;&nbsp;&nbsp;&nbsp;document.getElementById(section + '-section').classList.add('active');<br>
&nbsp;&nbsp;&nbsp;&nbsp;document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));<br>
&nbsp;&nbsp;&nbsp;&nbsp;link.parentElement.classList.add('active');<br>
&nbsp;&nbsp;});<br>
});
                </div>
                
                <p>5. اضغط Enter</p>
                <p>6. جرب النقر على الأقسام في الشريط الجانبي</p>
            </div>
        </div>
        
        <!-- حالة النظام -->
        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <div id="system-status">
                <div class="result warning">⏳ جاري فحص حالة النظام...</div>
            </div>
        </div>
    </div>

    <script>
        function showResult(message, type = 'success') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            
            // إزالة النتيجة بعد 5 ثوان
            setTimeout(() => {
                if (resultDiv.parentNode) {
                    resultDiv.parentNode.removeChild(resultDiv);
                }
            }, 5000);
        }

        function testAdminPanelLoad() {
            showResult('🧪 اختبار تحميل لوحة التحكم...', 'warning');
            
            fetch('admin-panel.html')
                .then(response => {
                    if (response.ok) {
                        showResult('✅ لوحة التحكم تحمل بنجاح', 'success');
                    } else {
                        showResult('❌ فشل في تحميل لوحة التحكم', 'error');
                    }
                })
                .catch(error => {
                    showResult('❌ خطأ في الاتصال: ' + error.message, 'error');
                });
        }

        function openAdminPanel() {
            window.open('admin-panel.html', '_blank');
            showResult('🚀 تم فتح لوحة التحكم الأصلية', 'success');
        }

        function openSimplePanel() {
            window.open('admin-panel-simple.html', '_blank');
            showResult('🚀 تم فتح لوحة التحكم المبسطة', 'success');
        }

        function openDebugPanel() {
            window.open('debug-admin-panel.html', '_blank');
            showResult('🚀 تم فتح أداة التشخيص', 'success');
        }

        function reloadFrame() {
            const frame = document.getElementById('admin-panel-frame');
            frame.src = frame.src;
            showResult('🔄 تم إعادة تحميل الإطار', 'success');
        }

        function testFrameNavigation() {
            showResult('🧪 اختبار التنقل في الإطار...', 'warning');
            
            const frame = document.getElementById('admin-panel-frame');
            
            try {
                const frameDoc = frame.contentDocument || frame.contentWindow.document;
                const navLinks = frameDoc.querySelectorAll('.nav-link');
                
                if (navLinks.length > 0) {
                    showResult(`✅ تم العثور على ${navLinks.length} رابط تنقل`, 'success');
                } else {
                    showResult('❌ لم يتم العثور على روابط التنقل', 'error');
                }
            } catch (error) {
                showResult('❌ لا يمكن الوصول لمحتوى الإطار: ' + error.message, 'error');
            }
        }

        function injectFix() {
            showResult('🔧 حقن الإصلاح في الإطار...', 'warning');
            
            const frame = document.getElementById('admin-panel-frame');
            
            try {
                const frameWindow = frame.contentWindow;
                const frameDoc = frame.contentDocument || frameWindow.document;
                
                // حقن كود الإصلاح
                const script = frameDoc.createElement('script');
                script.textContent = `
                    console.log('🔧 حقن الإصلاح...');
                    document.querySelectorAll('.nav-link').forEach(link => {
                        link.addEventListener('click', (e) => {
                            e.preventDefault();
                            const section = link.dataset.section;
                            console.log('🔄 التبديل إلى:', section);
                            
                            document.querySelectorAll('.content-section').forEach(s => s.classList.remove('active'));
                            const targetSection = document.getElementById(section + '-section');
                            if (targetSection) {
                                targetSection.classList.add('active');
                                console.log('✅ تم إظهار القسم:', section);
                            }
                            
                            document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                            link.parentElement.classList.add('active');
                        });
                    });
                    console.log('✅ تم حقن الإصلاح بنجاح');
                `;
                
                frameDoc.head.appendChild(script);
                showResult('✅ تم حقن الإصلاح بنجاح', 'success');
                
            } catch (error) {
                showResult('❌ فشل في حقن الإصلاح: ' + error.message, 'error');
            }
        }

        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            
            // فحص الملفات المطلوبة
            const files = [
                'admin-panel.html',
                'admin-panel.js',
                'admin-panel.css',
                'admin-panel-simple.html',
                'debug-admin-panel.html'
            ];
            
            let checkedFiles = 0;
            let availableFiles = 0;
            
            files.forEach(file => {
                fetch(file, { method: 'HEAD' })
                    .then(response => {
                        checkedFiles++;
                        if (response.ok) {
                            availableFiles++;
                        }
                        
                        if (checkedFiles === files.length) {
                            updateSystemStatus(availableFiles, files.length);
                        }
                    })
                    .catch(() => {
                        checkedFiles++;
                        if (checkedFiles === files.length) {
                            updateSystemStatus(availableFiles, files.length);
                        }
                    });
            });
        }

        function updateSystemStatus(available, total) {
            const statusDiv = document.getElementById('system-status');
            const percentage = Math.round((available / total) * 100);
            
            let statusClass = 'success';
            let statusText = '✅ جميع الملفات متاحة';
            
            if (percentage < 100) {
                statusClass = percentage > 50 ? 'warning' : 'error';
                statusText = `⚠️ ${available}/${total} ملفات متاحة (${percentage}%)`;
            }
            
            statusDiv.innerHTML = `<div class="result ${statusClass}">${statusText}</div>`;
        }

        // فحص حالة النظام عند التحميل
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
