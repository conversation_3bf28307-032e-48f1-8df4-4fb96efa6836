@echo off
chcp 65001 >nul
title إنشاء ترخيص تجريبي سريع - مؤسسة وقود المستقبل

echo.
echo ========================================
echo    إنشاء ترخيص تجريبي سريع
echo    مؤسسة وقود المستقبل
echo ========================================
echo.

echo جاري إنشاء ترخيص تجريبي...
echo.

:: إنشاء ملف JavaScript مؤقت لإنشاء الترخيص
echo const fs = require('fs'); > temp_license.js
echo const path = require('path'); >> temp_license.js
echo. >> temp_license.js
echo // إنشاء ترخيص تجريبي >> temp_license.js
echo function createTestLicense() { >> temp_license.js
echo     const deviceId = 'DEV-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9); >> temp_license.js
echo     const now = new Date(); >> temp_license.js
echo     const expiryDate = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000)); >> temp_license.js
echo. >> temp_license.js
echo     const testLicense = { >> temp_license.js
echo         key: 'FF-DEMO-2024-TEST', >> temp_license.js
echo         deviceId: deviceId, >> temp_license.js
echo         activationDate: now.toISOString(), >> temp_license.js
echo         expiryDate: expiryDate.toISOString(), >> temp_license.js
echo         status: 'active', >> temp_license.js
echo         type: 'demo', >> temp_license.js
echo         clientInfo: { >> temp_license.js
echo             firstName: 'مستخدم', >> temp_license.js
echo             lastName: 'تجريبي', >> temp_license.js
echo             phone: '**********', >> temp_license.js
echo             state: 'الجزائر', >> temp_license.js
echo             municipality: 'الجزائر الوسطى' >> temp_license.js
echo         }, >> temp_license.js
echo         signature: null >> temp_license.js
echo     }; >> temp_license.js
echo. >> temp_license.js
echo     // إنشاء التوقيع >> temp_license.js
echo     const dataString = testLicense.key + '-' + testLicense.deviceId + '-' + testLicense.expiryDate; >> temp_license.js
echo     testLicense.signature = Buffer.from(dataString).toString('base64').substr(0, 16); >> temp_license.js
echo. >> temp_license.js
echo     return testLicense; >> temp_license.js
echo } >> temp_license.js
echo. >> temp_license.js
echo // إنشاء الترخيص وحفظه >> temp_license.js
echo const license = createTestLicense(); >> temp_license.js
echo const licenseJson = JSON.stringify(license, null, 2); >> temp_license.js
echo. >> temp_license.js
echo // كتابة الترخيص إلى ملف >> temp_license.js
echo fs.writeFileSync('test-license.json', licenseJson); >> temp_license.js
echo. >> temp_license.js
echo console.log('✅ تم إنشاء الترخيص التجريبي بنجاح!'); >> temp_license.js
echo console.log('📄 مفتاح الترخيص:', license.key); >> temp_license.js
echo console.log('📅 تاريخ الانتهاء:', new Date(license.expiryDate).toLocaleDateString('ar-DZ')); >> temp_license.js
echo console.log('💾 تم حفظ الترخيص في ملف: test-license.json'); >> temp_license.js
echo console.log(''); >> temp_license.js
echo console.log('📋 لاستخدام الترخيص:'); >> temp_license.js
echo console.log('1. انسخ محتوى ملف test-license.json'); >> temp_license.js
echo console.log('2. افتح أدوات المطور في المتصفح (F12)'); >> temp_license.js
echo console.log('3. اذهب إلى تبويب Console'); >> temp_license.js
echo console.log('4. اكتب: localStorage.setItem("appLicense", `محتوى_الملف`)'); >> temp_license.js
echo console.log('5. أعد تحميل الصفحة'); >> temp_license.js

:: تشغيل الملف
node temp_license.js

:: عرض محتوى الترخيص
if exist test-license.json (
    echo.
    echo ========================================
    echo محتوى الترخيص التجريبي:
    echo ========================================
    type test-license.json
    echo.
    echo ========================================
    echo.
    echo 📋 تعليمات الاستخدام:
    echo.
    echo 1. انسخ المحتوى أعلاه (من { إلى })
    echo 2. افتح التطبيق في المتصفح
    echo 3. اضغط F12 لفتح أدوات المطور
    echo 4. اذهب إلى تبويب Console
    echo 5. اكتب الأمر التالي:
    echo    localStorage.setItem("appLicense", 'المحتوى_المنسوخ')
    echo 6. أعد تحميل الصفحة
    echo.
    echo ✅ ستتمكن الآن من تسجيل الدخول!
    echo.
) else (
    echo ❌ فشل في إنشاء الترخيص
)

:: تنظيف الملفات المؤقتة
if exist temp_license.js del temp_license.js

echo اضغط أي مفتاح للخروج...
pause >nul
