<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Electron الجديد - CFGPLProgram</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: var(--card-bg-color, white);
            border-radius: 15px;
            box-shadow: var(--box-shadow, 0 4px 15px rgba(0,0,0,0.1));
        }

        .test-section {
            background: var(--bg-color, #f8f9fa);
            border: 1px solid var(--border-color, #dee2e6);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            transition: all 0.3s ease;
        }

        .test-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .test-item.success {
            border-left-color: #27ae60;
        }

        .test-item.warning {
            border-left-color: #f39c12;
        }

        .test-item.danger {
            border-left-color: #e74c3c;
        }

        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
        }

        .status-pass {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            border: 1px solid rgba(39, 174, 96, 0.3);
        }

        .status-fail {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

        .status-pending {
            background: rgba(243, 156, 18, 0.1);
            color: #f39c12;
            border: 1px solid rgba(243, 156, 18, 0.3);
        }

        .test-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .test-btn.success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }

        .test-btn.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .test-btn.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .console-output {
            background: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }

        .electron-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .data-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--text-color, #333); margin-bottom: 30px;">
            ⚡ اختبار Electron الجديد - CFGPLProgram
        </h1>

        <!-- معلومات Electron -->
        <div class="electron-info" id="electron-info">
            <h3><i class="fas fa-desktop"></i> معلومات Electron الجديد</h3>
            <div id="electron-details">
                جاري فحص بيئة Electron الجديدة...
            </div>
        </div>

        <!-- اختبار APIs الجديدة -->
        <div class="test-section">
            <h3>🔌 اختبار APIs الجديدة</h3>

            <div class="test-item" id="electron-api-test">
                <span>فحص وجود electronAPI الجديد</span>
                <div>
                    <span class="status-badge status-pending" id="electron-api-status">في الانتظار</span>
                    <button class="test-btn" onclick="testElectronAPI()">اختبار</button>
                </div>
            </div>

            <div class="test-item" id="app-helpers-test">
                <span>فحص appHelpers</span>
                <div>
                    <span class="status-badge status-pending" id="app-helpers-status">في الانتظار</span>
                    <button class="test-btn" onclick="testAppHelpers()">اختبار</button>
                </div>
            </div>

            <div class="test-item" id="electron-app-test">
                <span>فحص electronApp</span>
                <div>
                    <span class="status-badge status-pending" id="electron-app-status">في الانتظار</span>
                    <button class="test-btn" onclick="testElectronApp()">اختبار</button>
                </div>
            </div>
        </div>

        <!-- اختبار إدارة البيانات الجديدة -->
        <div class="test-section">
            <h3>💾 اختبار إدارة البيانات الجديدة</h3>

            <div class="test-item" id="save-data-test">
                <span>حفظ البيانات الجديد</span>
                <div>
                    <span class="status-badge status-pending" id="save-data-status">في الانتظار</span>
                    <button class="test-btn" onclick="testSaveData()">اختبار</button>
                </div>
            </div>

            <div class="test-item" id="load-data-test">
                <span>تحميل البيانات الجديد</span>
                <div>
                    <span class="status-badge status-pending" id="load-data-status">في الانتظار</span>
                    <button class="test-btn" onclick="testLoadData()">اختبار</button>
                </div>
            </div>

            <div class="test-item" id="backup-test">
                <span>النسخ الاحتياطي المحسن</span>
                <div>
                    <span class="status-badge status-pending" id="backup-status">في الانتظار</span>
                    <button class="test-btn" onclick="testBackup()">اختبار</button>
                </div>
            </div>

            <div class="test-item" id="data-integrity-test">
                <span>فحص سلامة البيانات</span>
                <div>
                    <span class="status-badge status-pending" id="data-integrity-status">في الانتظار</span>
                    <button class="test-btn" onclick="testDataIntegrity()">اختبار</button>
                </div>
            </div>
        </div>

        <!-- اختبار الإشعارات المحسنة -->
        <div class="test-section">
            <h3>🔔 اختبار الإشعارات المحسنة</h3>

            <div class="test-item">
                <span>إشعار نجاح محسن</span>
                <button class="test-btn success" onclick="testEnhancedNotification('success')">اختبار</button>
            </div>

            <div class="test-item">
                <span>إشعار خطأ محسن</span>
                <button class="test-btn danger" onclick="testEnhancedNotification('error')">اختبار</button>
            </div>

            <div class="test-item">
                <span>إشعار معلومات محسن</span>
                <button class="test-btn" onclick="testEnhancedNotification('info')">اختبار</button>
            </div>
        </div>

        <!-- عرض البيانات -->
        <div class="test-section">
            <h3>📊 عرض البيانات المحملة</h3>

            <div class="test-item">
                <span>عرض بيانات الزبائن</span>
                <button class="test-btn" onclick="displayCustomersData()">عرض</button>
            </div>

            <div class="test-item">
                <span>عرض بيانات التراخيص</span>
                <button class="test-btn" onclick="displayLicensesData()">عرض</button>
            </div>

            <div class="test-item">
                <span>عرض الإحصائيات</span>
                <button class="test-btn" onclick="displayStatisticsData()">عرض</button>
            </div>

            <div class="data-display" id="data-display" style="display: none;">
                <h4>البيانات المعروضة:</h4>
                <pre id="data-content"></pre>
            </div>
        </div>

        <!-- مخرجات وحدة التحكم -->
        <div class="test-section">
            <h3>📋 مخرجات وحدة التحكم</h3>
            <div class="console-output" id="console-output">
                انقر على "بدء المراقبة" لعرض رسائل وحدة التحكم...
            </div>
            <button class="test-btn" onclick="startConsoleMonitoring()">بدء المراقبة</button>
            <button class="test-btn" onclick="clearConsole()">مسح</button>
        </div>

        <!-- اختبار شامل -->
        <div class="test-section">
            <h3>🚀 اختبار شامل</h3>
            <div style="text-align: center;">
                <button class="test-btn success" onclick="runAllTests()" style="padding: 15px 30px; font-size: 16px;">
                    تشغيل جميع الاختبارات
                </button>

                <button class="test-btn" onclick="openMainApp()" style="padding: 15px 30px; font-size: 16px; margin-right: 10px;">
                    فتح التطبيق الرئيسي
                </button>

                <button class="test-btn warning" onclick="initializeDefaultData()" style="padding: 15px 30px; font-size: 16px; margin-right: 10px;">
                    تهيئة البيانات الافتراضية
                </button>
            </div>
        </div>
    </div>

    <script src="scripts/electron-app.js"></script>
    <script>
        let consoleOutput = document.getElementById('console-output');
        let originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'warn' ? '#feca57' : '#48dbfb';
            consoleOutput.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function startConsoleMonitoring() {
            console.log = function(...args) {
                originalConsole.log.apply(console, args);
                addToConsole(args.join(' '), 'log');
            };

            console.error = function(...args) {
                originalConsole.error.apply(console, args);
                addToConsole(args.join(' '), 'error');
            };

            console.warn = function(...args) {
                originalConsole.warn.apply(console, args);
                addToConsole(args.join(' '), 'warn');
            };

            addToConsole('🔍 بدء مراقبة وحدة التحكم...', 'log');
        }

        function clearConsole() {
            consoleOutput.innerHTML = '';
        }

        function setTestStatus(testId, status) {
            const statusElement = document.getElementById(testId + '-status');
            if (statusElement) {
                statusElement.className = `status-badge status-${status}`;
                statusElement.textContent = status === 'pass' ? '✅ نجح' : status === 'fail' ? '❌ فشل' : '⏳ جاري...';
            }
        }

        function testElectronAPI() {
            setTestStatus('electron-api', 'pending');

            setTimeout(() => {
                if (typeof window.electronAPI !== 'undefined') {
                    setTestStatus('electron-api', 'pass');
                    addToConsole('✅ electronAPI الجديد متاح');
                } else {
                    setTestStatus('electron-api', 'fail');
                    addToConsole('❌ electronAPI الجديد غير متاح');
                }
            }, 500);
        }

        function testAppHelpers() {
            setTestStatus('app-helpers', 'pending');

            setTimeout(() => {
                if (typeof window.appHelpers !== 'undefined') {
                    setTestStatus('app-helpers', 'pass');
                    addToConsole('✅ appHelpers متاح');
                } else {
                    setTestStatus('app-helpers', 'fail');
                    addToConsole('❌ appHelpers غير متاح');
                }
            }, 500);
        }

        function testElectronApp() {
            setTestStatus('electron-app', 'pending');

            setTimeout(() => {
                if (typeof window.electronApp !== 'undefined') {
                    setTestStatus('electron-app', 'pass');
                    addToConsole('✅ electronApp متاح');
                } else {
                    setTestStatus('electron-app', 'fail');
                    addToConsole('❌ electronApp غير متاح');
                }
            }, 500);
        }

        async function testSaveData() {
            setTestStatus('save-data', 'pending');

            try {
                const testData = {
                    test: true,
                    timestamp: new Date().toISOString(),
                    message: 'اختبار حفظ البيانات الجديد'
                };

                if (window.electronAPI) {
                    const result = await window.electronAPI.saveData('test', testData);
                    if (result.success) {
                        setTestStatus('save-data', 'pass');
                        addToConsole('✅ تم حفظ البيانات بنجاح');
                    } else {
                        setTestStatus('save-data', 'fail');
                        addToConsole('❌ فشل في حفظ البيانات: ' + result.error);
                    }
                } else {
                    setTestStatus('save-data', 'fail');
                    addToConsole('❌ electronAPI غير متاح');
                }
            } catch (error) {
                setTestStatus('save-data', 'fail');
                addToConsole('❌ خطأ في اختبار حفظ البيانات: ' + error.message);
            }
        }

        async function testLoadData() {
            setTestStatus('load-data', 'pending');

            try {
                if (window.electronAPI) {
                    const result = await window.electronAPI.loadData('test');
                    if (result.success) {
                        setTestStatus('load-data', 'pass');
                        addToConsole('✅ تم تحميل البيانات بنجاح');
                    } else {
                        setTestStatus('load-data', 'pass');
                        addToConsole('✅ لا توجد بيانات محفوظة (طبيعي)');
                    }
                } else {
                    setTestStatus('load-data', 'fail');
                    addToConsole('❌ electronAPI غير متاح');
                }
            } catch (error) {
                setTestStatus('load-data', 'fail');
                addToConsole('❌ خطأ في اختبار تحميل البيانات: ' + error.message);
            }
        }

        async function testBackup() {
            setTestStatus('backup', 'pending');

            try {
                if (window.appHelpers) {
                    const result = await window.appHelpers.createBackupWithNotification();
                    if (result.success) {
                        setTestStatus('backup', 'pass');
                        addToConsole('✅ تم إنشاء النسخة الاحتياطية');
                    } else {
                        setTestStatus('backup', 'fail');
                        addToConsole('❌ فشل في إنشاء النسخة الاحتياطية: ' + result.error);
                    }
                } else {
                    setTestStatus('backup', 'fail');
                    addToConsole('❌ appHelpers غير متاح');
                }
            } catch (error) {
                setTestStatus('backup', 'fail');
                addToConsole('❌ خطأ في اختبار النسخ الاحتياطي: ' + error.message);
            }
        }

        async function testDataIntegrity() {
            setTestStatus('data-integrity', 'pending');

            try {
                if (window.appHelpers) {
                    const result = await window.appHelpers.checkDataIntegrity();
                    if (result && !result.error) {
                        setTestStatus('data-integrity', 'pass');
                        addToConsole('✅ فحص سلامة البيانات مكتمل');
                        addToConsole('📊 نتائج الفحص: ' + JSON.stringify(result, null, 2));
                    } else {
                        setTestStatus('data-integrity', 'fail');
                        addToConsole('❌ خطأ في فحص سلامة البيانات: ' + (result.error || 'خطأ غير معروف'));
                    }
                } else {
                    setTestStatus('data-integrity', 'fail');
                    addToConsole('❌ appHelpers غير متاح');
                }
            } catch (error) {
                setTestStatus('data-integrity', 'fail');
                addToConsole('❌ خطأ في اختبار سلامة البيانات: ' + error.message);
            }
        }

        async function testEnhancedNotification(type) {
            const messages = {
                success: 'اختبار إشعار النجاح المحسن',
                error: 'اختبار إشعار الخطأ المحسن',
                info: 'اختبار إشعار المعلومات المحسن'
            };

            try {
                if (window.appHelpers) {
                    switch (type) {
                        case 'success':
                            await window.appHelpers.showSuccessNotification(messages[type]);
                            break;
                        case 'error':
                            await window.appHelpers.showErrorNotification(messages[type]);
                            break;
                        case 'info':
                            await window.appHelpers.showInfoNotification(messages[type]);
                            break;
                    }
                    addToConsole(`🔔 تم إرسال إشعار ${type} محسن`);
                } else {
                    addToConsole('❌ appHelpers غير متاح');
                }
            } catch (error) {
                addToConsole(`❌ خطأ في إرسال إشعار ${type}: ` + error.message);
            }
        }

        async function displayCustomersData() {
            try {
                if (window.electronApp && window.electronApp.appData) {
                    const customers = window.electronApp.appData.customers;
                    displayData('بيانات الزبائن', customers);
                } else if (window.appHelpers) {
                    const result = await window.appHelpers.loadCustomers();
                    displayData('بيانات الزبائن', result.data || []);
                } else {
                    addToConsole('❌ لا يمكن الوصول لبيانات الزبائن');
                }
            } catch (error) {
                addToConsole('❌ خطأ في عرض بيانات الزبائن: ' + error.message);
            }
        }

        async function displayLicensesData() {
            try {
                if (window.electronApp && window.electronApp.appData) {
                    const licenses = window.electronApp.appData.licenses;
                    displayData('بيانات التراخيص', licenses);
                } else if (window.appHelpers) {
                    const result = await window.appHelpers.loadLicenses();
                    displayData('بيانات التراخيص', result.data || []);
                } else {
                    addToConsole('❌ لا يمكن الوصول لبيانات التراخيص');
                }
            } catch (error) {
                addToConsole('❌ خطأ في عرض بيانات التراخيص: ' + error.message);
            }
        }

        async function displayStatisticsData() {
            try {
                if (window.electronApp && window.electronApp.appData) {
                    const statistics = window.electronApp.appData.statistics;
                    displayData('الإحصائيات', statistics);
                } else if (window.appHelpers) {
                    const result = await window.appHelpers.loadStatistics();
                    displayData('الإحصائيات', result.data || {});
                } else {
                    addToConsole('❌ لا يمكن الوصول للإحصائيات');
                }
            } catch (error) {
                addToConsole('❌ خطأ في عرض الإحصائيات: ' + error.message);
            }
        }

        function displayData(title, data) {
            const dataDisplay = document.getElementById('data-display');
            const dataContent = document.getElementById('data-content');

            dataContent.textContent = JSON.stringify(data, null, 2);
            dataDisplay.style.display = 'block';

            addToConsole(`📊 تم عرض ${title}`);
        }

        async function initializeDefaultData() {
            try {
                if (window.appHelpers) {
                    const result = await window.appHelpers.initializeDefaultData();
                    if (result.success) {
                        addToConsole('✅ تم تهيئة البيانات الافتراضية بنجاح');
                    } else {
                        addToConsole('❌ فشل في تهيئة البيانات الافتراضية: ' + result.error);
                    }
                } else {
                    addToConsole('❌ appHelpers غير متاح');
                }
            } catch (error) {
                addToConsole('❌ خطأ في تهيئة البيانات الافتراضية: ' + error.message);
            }
        }

        function runAllTests() {
            addToConsole('🚀 بدء الاختبار الشامل للنظام الجديد...');

            setTimeout(() => testElectronAPI(), 500);
            setTimeout(() => testAppHelpers(), 1000);
            setTimeout(() => testElectronApp(), 1500);
            setTimeout(() => testSaveData(), 2000);
            setTimeout(() => testLoadData(), 2500);
            setTimeout(() => testBackup(), 3000);
            setTimeout(() => testDataIntegrity(), 3500);

            setTimeout(() => {
                addToConsole('✅ انتهى الاختبار الشامل');
            }, 4500);
        }

        function openMainApp() {
            window.location.href = 'index.html';
        }

        function updateElectronInfo() {
            const infoDiv = document.getElementById('electron-details');

            if (typeof window.electronAPI !== 'undefined') {
                window.electronAPI.getSystemInfo().then(info => {
                    infoDiv.innerHTML = `
                        <p><strong>البيئة:</strong> Electron الجديد ✅</p>
                        <p><strong>النظام:</strong> ${info.platform}</p>
                        <p><strong>المعمارية:</strong> ${info.arch}</p>
                        <p><strong>إصدار التطبيق:</strong> ${info.version}</p>
                        <p><strong>إصدار Electron:</strong> ${info.electronVersion}</p>
                        <p><strong>إصدار Node.js:</strong> ${info.nodeVersion}</p>
                        <p><strong>مسار البيانات:</strong> ${info.userDataPath}</p>
                    `;
                }).catch(() => {
                    infoDiv.innerHTML = '<p><strong>البيئة:</strong> Electron (معلومات محدودة)</p>';
                });
            } else {
                infoDiv.innerHTML = '<p><strong>البيئة:</strong> متصفح ويب عادي</p>';
            }
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            addToConsole('📋 صفحة اختبار Electron الجديد جاهزة');
            startConsoleMonitoring();
            updateElectronInfo();

            setTimeout(() => {
                addToConsole('🔍 بدء الفحص التلقائي...');
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>