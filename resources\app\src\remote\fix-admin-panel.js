// ملف إصلاح شامل للوحة التحكم
// يحل مشاكل التضارب بين الدوال القديمة والجديدة

console.log('🔧 بدء إصلاح لوحة التحكم...');

// 1. تنظيف المتغيرات العامة
function cleanupGlobalVariables() {
    console.log('🧹 تنظيف المتغيرات العامة...');
    
    // حذف المتغيرات المتضاربة
    if (window.licenseManager && window.adminPanel && window.adminPanel.licenseManager) {
        delete window.licenseManager;
        console.log('✅ تم حذف licenseManager المتضارب');
    }
}

// 2. إصلاح دالة switchSection
function fixSwitchSection() {
    console.log('🔧 إصلاح دالة switchSection...');
    
    if (window.adminPanel && typeof window.adminPanel.switchSection === 'function') {
        const originalSwitchSection = window.adminPanel.switchSection.bind(window.adminPanel);
        
        window.adminPanel.switchSection = function(sectionName) {
            try {
                console.log(`🔄 محاولة التبديل إلى: ${sectionName}`);
                
                // التحقق من وجود القسم
                const targetSection = document.getElementById(`${sectionName}-section`);
                if (!targetSection) {
                    console.error(`❌ القسم غير موجود: ${sectionName}-section`);
                    return false;
                }
                
                // تنفيذ الدالة الأصلية
                originalSwitchSection(sectionName);
                
                console.log(`✅ تم التبديل بنجاح إلى: ${sectionName}`);
                return true;
                
            } catch (error) {
                console.error(`❌ خطأ في التبديل إلى ${sectionName}:`, error);
                
                // محاولة إصلاح بسيط
                this.simpleSwitchSection(sectionName);
                return false;
            }
        };
        
        // دالة تبديل بسيطة كبديل
        window.adminPanel.simpleSwitchSection = function(sectionName) {
            console.log(`🔄 استخدام التبديل البسيط إلى: ${sectionName}`);
            
            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // إظهار القسم المطلوب
            const targetSection = document.getElementById(`${sectionName}-section`);
            if (targetSection) {
                targetSection.classList.add('active');
                
                // تحديث التنقل
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                const navItem = document.querySelector(`[data-section="${sectionName}"]`);
                if (navItem && navItem.parentElement) {
                    navItem.parentElement.classList.add('active');
                }
                
                // تحديث العنوان
                const titles = {
                    dashboard: 'لوحة المعلومات',
                    licenses: 'إدارة التراخيص',
                    clients: 'العملاء',
                    activations: 'طلبات التفعيل',
                    monitoring: 'المراقبة',
                    settings: 'الإعدادات'
                };
                
                const pageTitle = document.getElementById('page-title');
                if (pageTitle) {
                    pageTitle.textContent = titles[sectionName] || 'لوحة التحكم';
                }
                
                this.currentSection = sectionName;
                console.log(`✅ تم التبديل البسيط إلى: ${sectionName}`);
            }
        };
        
        console.log('✅ تم إصلاح دالة switchSection');
    }
}

// 3. إصلاح أحداث التنقل
function fixNavigationEvents() {
    console.log('🔧 إصلاح أحداث التنقل...');
    
    // إزالة جميع المستمعين القدامى
    document.querySelectorAll('.nav-link').forEach(link => {
        const newLink = link.cloneNode(true);
        link.parentNode.replaceChild(newLink, link);
    });
    
    // إضافة مستمعين جدد
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const section = link.dataset.section;
            
            if (window.adminPanel && typeof window.adminPanel.switchSection === 'function') {
                window.adminPanel.switchSection(section);
            } else {
                console.error('❌ adminPanel أو switchSection غير متاح');
            }
        });
    });
    
    console.log('✅ تم إصلاح أحداث التنقل');
}

// 4. إصلاح مدير التراخيص
function fixLicenseManager() {
    console.log('🔧 إصلاح مدير التراخيص...');
    
    if (window.adminPanel && typeof LicenseManager !== 'undefined') {
        try {
            if (!window.adminPanel.licenseManager) {
                window.adminPanel.licenseManager = new LicenseManager();
                console.log('✅ تم إنشاء مدير التراخيص');
            }
            
            // ربط الدوال المطلوبة
            window.licenseManager = window.adminPanel.licenseManager;
            
        } catch (error) {
            console.error('❌ خطأ في إصلاح مدير التراخيص:', error);
        }
    }
}

// 5. إصلاح تحميل البيانات
function fixDataLoading() {
    console.log('🔧 إصلاح تحميل البيانات...');
    
    if (window.adminPanel) {
        // إصلاح دالة loadSectionData
        const originalLoadSectionData = window.adminPanel.loadSectionData;
        
        window.adminPanel.loadSectionData = function(sectionName) {
            try {
                console.log(`📂 تحميل بيانات القسم: ${sectionName}`);
                
                switch (sectionName) {
                    case 'dashboard':
                        this.updateStatistics();
                        break;
                    case 'licenses':
                        if (this.licenseManager && typeof this.licenseManager.loadLicenses === 'function') {
                            this.licenseManager.loadLicenses();
                            this.licenseManager.updateLicenseStats();
                        }
                        break;
                    case 'clients':
                        this.updateClientsGrid();
                        break;
                    case 'activations':
                        this.updateActivationRequests();
                        break;
                    default:
                        console.log(`📋 قسم ${sectionName} لا يحتاج تحميل بيانات خاص`);
                }
                
                console.log(`✅ تم تحميل بيانات ${sectionName}`);
                
            } catch (error) {
                console.error(`❌ خطأ في تحميل بيانات ${sectionName}:`, error);
            }
        };
    }
}

// 6. فحص وإصلاح العناصر المفقودة
function checkAndFixMissingElements() {
    console.log('🔍 فحص العناصر المفقودة...');
    
    const requiredElements = [
        'page-title',
        'dashboard-section',
        'licenses-section',
        'clients-section',
        'activations-section'
    ];
    
    requiredElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (!element) {
            console.warn(`⚠️ عنصر مفقود: ${elementId}`);
        } else {
            console.log(`✅ عنصر موجود: ${elementId}`);
        }
    });
    
    // فحص روابط التنقل
    const navLinks = document.querySelectorAll('.nav-link');
    console.log(`📊 عدد روابط التنقل: ${navLinks.length}`);
    
    // فحص أقسام المحتوى
    const contentSections = document.querySelectorAll('.content-section');
    console.log(`📊 عدد أقسام المحتوى: ${contentSections.length}`);
}

// 7. دالة الإصلاح الشامل
function runComprehensiveFix() {
    console.log('🚀 بدء الإصلاح الشامل للوحة التحكم...');
    
    try {
        // تنفيذ الإصلاحات بالتسلسل
        cleanupGlobalVariables();
        
        setTimeout(() => {
            fixSwitchSection();
            fixNavigationEvents();
            fixLicenseManager();
            fixDataLoading();
            checkAndFixMissingElements();
            
            console.log('✅ انتهى الإصلاح الشامل بنجاح!');
            
            // اختبار التنقل
            setTimeout(() => {
                testNavigation();
            }, 1000);
            
        }, 500);
        
    } catch (error) {
        console.error('❌ خطأ في الإصلاح الشامل:', error);
    }
}

// 8. اختبار التنقل
function testNavigation() {
    console.log('🧪 اختبار التنقل...');
    
    const sections = ['dashboard', 'licenses', 'clients', 'activations'];
    let currentIndex = 0;
    
    function testNextSection() {
        if (currentIndex < sections.length) {
            const section = sections[currentIndex];
            console.log(`🧪 اختبار القسم: ${section}`);
            
            if (window.adminPanel && typeof window.adminPanel.switchSection === 'function') {
                window.adminPanel.switchSection(section);
            }
            
            currentIndex++;
            setTimeout(testNextSection, 1000);
        } else {
            console.log('✅ انتهى اختبار التنقل');
            // العودة إلى لوحة المعلومات
            if (window.adminPanel) {
                window.adminPanel.switchSection('dashboard');
            }
        }
    }
    
    testNextSection();
}

// 9. تصدير الدوال للاستخدام الخارجي
window.adminPanelFix = {
    runComprehensiveFix,
    fixSwitchSection,
    fixNavigationEvents,
    fixLicenseManager,
    testNavigation,
    checkAndFixMissingElements
};

// 10. تشغيل الإصلاح تلقائياً عند التحميل
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runComprehensiveFix, 2000);
    });
} else {
    setTimeout(runComprehensiveFix, 1000);
}

console.log('📋 ملف الإصلاح جاهز. استخدم window.adminPanelFix.runComprehensiveFix() لتشغيل الإصلاح يدوياً');
