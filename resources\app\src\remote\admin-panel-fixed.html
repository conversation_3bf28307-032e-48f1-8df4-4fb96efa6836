<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المُصلحة - CFGPLProgram</title>
    <link rel="stylesheet" href="admin-panel.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* إصلاحات CSS إضافية */
        .content-section {
            display: none;
        }
        
        .content-section.active {
            display: block;
        }
        
        .nav-item.active {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .nav-link {
            color: inherit;
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .fix-status {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 10000;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .fix-status.error {
            background: #dc3545;
        }
        
        .fix-status.warning {
            background: #ffc107;
            color: #212529;
        }
    </style>
</head>
<body>
    <!-- مؤشر حالة الإصلاح -->
    <div id="fix-status" class="fix-status warning">
        🔧 جاري تطبيق الإصلاحات...
    </div>

    <div class="admin-container">
        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>مؤسسة وقود المستقبل</p>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="#dashboard" class="nav-link" data-section="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة المعلومات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#licenses" class="nav-link" data-section="licenses">
                            <i class="fas fa-key"></i>
                            <span>إدارة التراخيص</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#clients" class="nav-link" data-section="clients">
                            <i class="fas fa-users"></i>
                            <span>العملاء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#activations" class="nav-link" data-section="activations">
                            <i class="fas fa-user-plus"></i>
                            <span>طلبات التفعيل</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#monitoring" class="nav-link" data-section="monitoring">
                            <i class="fas fa-chart-line"></i>
                            <span>المراقبة</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#settings" class="nav-link" data-section="settings">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="page-title">لوحة المعلومات</h1>
                </div>
                <div class="header-right">
                    <span id="current-time"></span>
                    <button class="btn secondary" id="logout-btn">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </button>
                </div>
            </header>

            <!-- المحتوى -->
            <div class="content-area">
                <!-- لوحة المعلومات -->
                <section id="dashboard-section" class="content-section active">
                    <h2>📊 لوحة المعلومات</h2>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; text-align: center;">
                            <i class="fas fa-key" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <h3>التراخيص النشطة</h3>
                            <p style="font-size: 2.5rem; font-weight: bold; margin: 10px 0;">15</p>
                            <small>+3 هذا الشهر</small>
                        </div>
                        
                        <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 30px; border-radius: 15px; text-align: center;">
                            <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <h3>العملاء</h3>
                            <p style="font-size: 2.5rem; font-weight: bold; margin: 10px 0;">42</p>
                            <small>+7 هذا الأسبوع</small>
                        </div>
                        
                        <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 30px; border-radius: 15px; text-align: center;">
                            <i class="fas fa-clipboard-list" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <h3>طلبات التفعيل</h3>
                            <p style="font-size: 2.5rem; font-weight: bold; margin: 10px 0;">8</p>
                            <small>في الانتظار</small>
                        </div>
                        
                        <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 30px; border-radius: 15px; text-align: center;">
                            <i class="fas fa-chart-line" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <h3>الإيرادات</h3>
                            <p style="font-size: 2.5rem; font-weight: bold; margin: 10px 0;">$2,450</p>
                            <small>هذا الشهر</small>
                        </div>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-top: 20px;">
                        <h3>📈 نشاط حديث</h3>
                        <ul style="list-style: none; padding: 0;">
                            <li style="padding: 10px 0; border-bottom: 1px solid #dee2e6;">
                                <i class="fas fa-plus-circle" style="color: #28a745; margin-left: 10px;"></i>
                                تم إنشاء ترخيص جديد للعميل أحمد محمد
                            </li>
                            <li style="padding: 10px 0; border-bottom: 1px solid #dee2e6;">
                                <i class="fas fa-user-plus" style="color: #007bff; margin-left: 10px;"></i>
                                تم تسجيل عميل جديد: فاطمة علي
                            </li>
                            <li style="padding: 10px 0;">
                                <i class="fas fa-check-circle" style="color: #28a745; margin-left: 10px;"></i>
                                تم تفعيل ترخيص العميل محمد حسن
                            </li>
                        </ul>
                    </div>
                </section>

                <!-- إدارة التراخيص -->
                <section id="licenses-section" class="content-section">
                    <h2>🔑 إدارة التراخيص</h2>
                    
                    <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;">
                        <h3>إحصائيات التراخيص</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px;">
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #28a745;">15</div>
                                <div style="font-size: 0.9rem; color: #666;">نشطة</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #dc3545;">3</div>
                                <div style="font-size: 0.9rem; color: #666;">منتهية</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #ffc107;">2</div>
                                <div style="font-size: 0.9rem; color: #666;">معلقة</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #17a2b8;">5</div>
                                <div style="font-size: 0.9rem; color: #666;">تنتهي قريباً</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin: 20px 0;">
                        <button style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 8px; margin: 5px; cursor: pointer;">
                            <i class="fas fa-plus"></i> إنشاء ترخيص جديد
                        </button>
                        <button style="background: #17a2b8; color: white; border: none; padding: 12px 24px; border-radius: 8px; margin: 5px; cursor: pointer;">
                            <i class="fas fa-magic"></i> إنشاء ترخيص تجريبي
                        </button>
                        <button style="background: #ffc107; color: #212529; border: none; padding: 12px 24px; border-radius: 8px; margin: 5px; cursor: pointer;">
                            <i class="fas fa-download"></i> تصدير التراخيص
                        </button>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                        <h4>جدول التراخيص</h4>
                        <p>هنا ستظهر قائمة مفصلة بجميع التراخيص مع إمكانيات الفلترة والبحث والإدارة.</p>
                    </div>
                </section>

                <!-- العملاء -->
                <section id="clients-section" class="content-section">
                    <h2>👥 إدارة العملاء</h2>
                    
                    <div style="background: #f3e5f5; padding: 20px; border-radius: 10px; margin: 20px 0;">
                        <h3>إحصائيات العملاء</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px;">
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #7b1fa2;">42</div>
                                <div style="font-size: 0.9rem; color: #666;">إجمالي العملاء</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #28a745;">38</div>
                                <div style="font-size: 0.9rem; color: #666;">نشطين</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #17a2b8;">7</div>
                                <div style="font-size: 0.9rem; color: #666;">جدد هذا الشهر</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                        <h4>قائمة العملاء</h4>
                        <p>هنا ستظهر قائمة مفصلة بجميع العملاء مع معلومات الاتصال وحالة التراخيص.</p>
                    </div>
                </section>

                <!-- طلبات التفعيل -->
                <section id="activations-section" class="content-section">
                    <h2>📋 طلبات التفعيل</h2>
                    
                    <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0;">
                        <h3>طلبات في الانتظار</h3>
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-top: 15px;">
                            <p><strong>8 طلبات</strong> تحتاج إلى مراجعة وموافقة</p>
                        </div>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                        <h4>قائمة طلبات التفعيل</h4>
                        <p>هنا ستظهر جميع طلبات التفعيل الجديدة مع إمكانية الموافقة أو الرفض.</p>
                    </div>
                </section>

                <!-- المراقبة -->
                <section id="monitoring-section" class="content-section">
                    <h2>📈 المراقبة والتحليلات</h2>
                    
                    <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;">
                        <h3>حالة النظام</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="color: #28a745; font-size: 1.2rem; margin-bottom: 5px;">
                                    <i class="fas fa-check-circle"></i> متصل
                                </div>
                                <div style="font-size: 0.9rem; color: #666;">حالة الخادم</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="color: #17a2b8; font-size: 1.2rem; margin-bottom: 5px;">
                                    <i class="fas fa-database"></i> 99.9%
                                </div>
                                <div style="font-size: 0.9rem; color: #666;">قاعدة البيانات</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                        <h4>التحليلات والتقارير</h4>
                        <p>هنا ستظهر الرسوم البيانية والتحليلات المفصلة لاستخدام النظام.</p>
                    </div>
                </section>

                <!-- الإعدادات -->
                <section id="settings-section" class="content-section">
                    <h2>⚙️ إعدادات النظام</h2>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                        <h3>الإعدادات العامة</h3>
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-top: 15px;">
                            <p>هنا يمكن تخصيص إعدادات النظام والأمان والإشعارات.</p>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <script>
        // نظام تنقل مضمون ومبسط
        console.log('🚀 بدء تهيئة لوحة التحكم المُصلحة...');
        
        let currentSection = 'dashboard';
        
        function updateFixStatus(message, type = 'success') {
            const statusDiv = document.getElementById('fix-status');
            statusDiv.textContent = message;
            statusDiv.className = `fix-status ${type}`;
            
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }
        
        function switchSection(sectionName) {
            console.log(`🔄 التبديل إلى القسم: ${sectionName}`);
            
            try {
                // إخفاء جميع الأقسام
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                });
                
                // إظهار القسم المطلوب
                const targetSection = document.getElementById(`${sectionName}-section`);
                if (targetSection) {
                    targetSection.classList.add('active');
                    console.log(`✅ تم إظهار القسم: ${sectionName}`);
                } else {
                    console.error(`❌ لم يتم العثور على القسم: ${sectionName}`);
                    updateFixStatus(`❌ القسم ${sectionName} غير موجود`, 'error');
                    return false;
                }
                
                // تحديث التنقل
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                const navLink = document.querySelector(`[data-section="${sectionName}"]`);
                if (navLink && navLink.parentElement) {
                    navLink.parentElement.classList.add('active');
                }
                
                // تحديث العنوان
                const titles = {
                    dashboard: 'لوحة المعلومات',
                    licenses: 'إدارة التراخيص',
                    clients: 'العملاء',
                    activations: 'طلبات التفعيل',
                    monitoring: 'المراقبة',
                    settings: 'الإعدادات'
                };
                
                const pageTitle = document.getElementById('page-title');
                if (pageTitle) {
                    pageTitle.textContent = titles[sectionName] || 'لوحة التحكم';
                }
                
                currentSection = sectionName;
                updateFixStatus(`✅ تم التبديل إلى ${titles[sectionName]}`, 'success');
                return true;
                
            } catch (error) {
                console.error('❌ خطأ في التبديل:', error);
                updateFixStatus(`❌ خطأ في التبديل: ${error.message}`, 'error');
                return false;
            }
        }
        
        function setupNavigation() {
            console.log('🔧 إعداد التنقل...');
            
            const navLinks = document.querySelectorAll('.nav-link');
            console.log(`📊 عدد روابط التنقل: ${navLinks.length}`);
            
            if (navLinks.length === 0) {
                updateFixStatus('❌ لم يتم العثور على روابط التنقل', 'error');
                return false;
            }
            
            navLinks.forEach((link, index) => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const section = link.dataset.section;
                    console.log(`🖱️ نقر على رابط: ${section}`);
                    switchSection(section);
                });
                
                console.log(`✅ تم ربط الرابط ${index + 1}: ${link.dataset.section}`);
            });
            
            updateFixStatus('✅ تم إعداد التنقل بنجاح', 'success');
            return true;
        }
        
        function updateTime() {
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = new Date().toLocaleTimeString('ar-DZ');
            }
        }
        
        function init() {
            console.log('🔧 تهيئة لوحة التحكم...');
            
            // إعداد التنقل
            if (!setupNavigation()) {
                console.error('❌ فشل في إعداد التنقل');
                return;
            }
            
            // تحديث الوقت
            updateTime();
            setInterval(updateTime, 1000);
            
            // إعداد أزرار أخرى
            const logoutBtn = document.getElementById('logout-btn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', () => {
                    if (confirm('هل تريد تسجيل الخروج؟')) {
                        window.location.href = '../auth/login.html';
                    }
                });
            }
            
            console.log('✅ تم تهيئة لوحة التحكم بنجاح');
            updateFixStatus('✅ لوحة التحكم جاهزة للاستخدام', 'success');
        }
        
        // تشغيل التهيئة عند تحميل الصفحة
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init();
        }
        
        // تصدير الدوال للاستخدام الخارجي
        window.switchSection = switchSection;
        window.adminPanelFixed = {
            switchSection,
            setupNavigation,
            currentSection: () => currentSection
        };
        
        console.log('📋 لوحة التحكم المُصلحة جاهزة');
    </script>
</body>
</html>
