<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص لوحة التحكم - CFGPLProgram</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .debug-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .debug-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .debug-item.success {
            border-left-color: #28a745;
        }
        
        .debug-item.error {
            border-left-color: #dc3545;
        }
        
        .debug-item.warning {
            border-left-color: #ffc107;
        }
        
        .status-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .test-btn:hover {
            background: #0056b3;
        }
        
        .console-output {
            background: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
            🔧 تشخيص لوحة التحكم
        </h1>
        
        <!-- فحص العناصر الأساسية -->
        <div class="debug-section">
            <h3>🔍 فحص العناصر الأساسية</h3>
            
            <div class="debug-item" id="nav-links-check">
                <span>روابط التنقل (.nav-link)</span>
                <div>
                    <span class="status-badge status-warning" id="nav-links-status">جاري الفحص...</span>
                    <button class="test-btn" onclick="checkNavLinks()">فحص</button>
                </div>
            </div>
            
            <div class="debug-item" id="content-sections-check">
                <span>أقسام المحتوى (.content-section)</span>
                <div>
                    <span class="status-badge status-warning" id="content-sections-status">جاري الفحص...</span>
                    <button class="test-btn" onclick="checkContentSections()">فحص</button>
                </div>
            </div>
            
            <div class="debug-item" id="admin-panel-check">
                <span>كائن AdminPanel</span>
                <div>
                    <span class="status-badge status-warning" id="admin-panel-status">جاري الفحص...</span>
                    <button class="test-btn" onclick="checkAdminPanel()">فحص</button>
                </div>
            </div>
            
            <div class="debug-item" id="license-manager-check">
                <span>كائن LicenseManager</span>
                <div>
                    <span class="status-badge status-warning" id="license-manager-status">جاري الفحص...</span>
                    <button class="test-btn" onclick="checkLicenseManager()">فحص</button>
                </div>
            </div>
        </div>
        
        <!-- اختبار التنقل -->
        <div class="debug-section">
            <h3>🧪 اختبار التنقل</h3>
            
            <div class="debug-item">
                <span>اختبار التبديل إلى لوحة المعلومات</span>
                <button class="test-btn" onclick="testSwitchSection('dashboard')">اختبار</button>
            </div>
            
            <div class="debug-item">
                <span>اختبار التبديل إلى إدارة التراخيص</span>
                <button class="test-btn" onclick="testSwitchSection('licenses')">اختبار</button>
            </div>
            
            <div class="debug-item">
                <span>اختبار التبديل إلى العملاء</span>
                <button class="test-btn" onclick="testSwitchSection('clients')">اختبار</button>
            </div>
            
            <div class="debug-item">
                <span>اختبار التبديل إلى طلبات التفعيل</span>
                <button class="test-btn" onclick="testSwitchSection('activations')">اختبار</button>
            </div>
        </div>
        
        <!-- مخرجات وحدة التحكم -->
        <div class="debug-section">
            <h3>📋 مخرجات وحدة التحكم</h3>
            <div class="console-output" id="console-output">
                انقر على "بدء المراقبة" لعرض رسائل وحدة التحكم...
            </div>
            <button class="test-btn" onclick="startConsoleMonitoring()">بدء المراقبة</button>
            <button class="test-btn" onclick="clearConsole()">مسح</button>
        </div>
        
        <!-- إجراءات الإصلاح -->
        <div class="debug-section">
            <h3>🔧 إجراءات الإصلاح</h3>
            
            <div class="debug-item">
                <span>إعادة تهيئة لوحة التحكم</span>
                <button class="test-btn" onclick="reinitializeAdminPanel()">تنفيذ</button>
            </div>
            
            <div class="debug-item">
                <span>إعادة تحميل الصفحة</span>
                <button class="test-btn" onclick="location.reload()">تنفيذ</button>
            </div>
            
            <div class="debug-item">
                <span>فتح لوحة التحكم الأصلية</span>
                <button class="test-btn" onclick="window.open('admin-panel.html', '_blank')">فتح</button>
            </div>
        </div>
        
        <!-- تشخيص سريع -->
        <div class="debug-section">
            <h3>⚡ تشخيص سريع</h3>
            <button class="test-btn" onclick="runQuickDiagnosis()" style="padding: 12px 24px; font-size: 14px;">
                🚀 تشغيل التشخيص الشامل
            </button>
        </div>
    </div>

    <script>
        let consoleOutput = document.getElementById('console-output');
        let originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'warn' ? '#feca57' : '#48dbfb';
            consoleOutput.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function startConsoleMonitoring() {
            console.log = function(...args) {
                originalConsole.log.apply(console, args);
                addToConsole(args.join(' '), 'log');
            };
            
            console.error = function(...args) {
                originalConsole.error.apply(console, args);
                addToConsole(args.join(' '), 'error');
            };
            
            console.warn = function(...args) {
                originalConsole.warn.apply(console, args);
                addToConsole(args.join(' '), 'warn');
            };
            
            addToConsole('🔍 بدء مراقبة وحدة التحكم...', 'log');
        }

        function clearConsole() {
            consoleOutput.innerHTML = '';
        }

        function checkNavLinks() {
            const navLinks = document.querySelectorAll('.nav-link');
            const status = document.getElementById('nav-links-status');
            
            if (navLinks.length > 0) {
                status.textContent = `✅ موجود (${navLinks.length})`;
                status.className = 'status-badge status-success';
                addToConsole(`✅ تم العثور على ${navLinks.length} رابط تنقل`);
            } else {
                status.textContent = '❌ غير موجود';
                status.className = 'status-badge status-error';
                addToConsole('❌ لم يتم العثور على روابط التنقل');
            }
        }

        function checkContentSections() {
            const sections = document.querySelectorAll('.content-section');
            const status = document.getElementById('content-sections-status');
            
            if (sections.length > 0) {
                status.textContent = `✅ موجود (${sections.length})`;
                status.className = 'status-badge status-success';
                addToConsole(`✅ تم العثور على ${sections.length} قسم محتوى`);
            } else {
                status.textContent = '❌ غير موجود';
                status.className = 'status-badge status-error';
                addToConsole('❌ لم يتم العثور على أقسام المحتوى');
            }
        }

        function checkAdminPanel() {
            const status = document.getElementById('admin-panel-status');
            
            if (typeof window.adminPanel !== 'undefined') {
                status.textContent = '✅ موجود';
                status.className = 'status-badge status-success';
                addToConsole('✅ كائن AdminPanel موجود ومتاح');
            } else {
                status.textContent = '❌ غير موجود';
                status.className = 'status-badge status-error';
                addToConsole('❌ كائن AdminPanel غير موجود');
            }
        }

        function checkLicenseManager() {
            const status = document.getElementById('license-manager-status');
            
            if (typeof window.licenseManager !== 'undefined' || 
                (window.adminPanel && window.adminPanel.licenseManager)) {
                status.textContent = '✅ موجود';
                status.className = 'status-badge status-success';
                addToConsole('✅ كائن LicenseManager موجود ومتاح');
            } else {
                status.textContent = '❌ غير موجود';
                status.className = 'status-badge status-error';
                addToConsole('❌ كائن LicenseManager غير موجود');
            }
        }

        function testSwitchSection(sectionName) {
            addToConsole(`🧪 اختبار التبديل إلى القسم: ${sectionName}`);
            
            if (window.adminPanel && typeof window.adminPanel.switchSection === 'function') {
                try {
                    window.adminPanel.switchSection(sectionName);
                    addToConsole(`✅ تم التبديل إلى ${sectionName} بنجاح`);
                } catch (error) {
                    addToConsole(`❌ خطأ في التبديل إلى ${sectionName}: ${error.message}`, 'error');
                }
            } else {
                addToConsole('❌ دالة switchSection غير متاحة', 'error');
            }
        }

        function reinitializeAdminPanel() {
            addToConsole('🔄 إعادة تهيئة لوحة التحكم...');
            
            try {
                if (typeof AdminPanel !== 'undefined') {
                    window.adminPanel = new AdminPanel();
                    addToConsole('✅ تم إعادة تهيئة لوحة التحكم بنجاح');
                } else {
                    addToConsole('❌ فئة AdminPanel غير متاحة', 'error');
                }
            } catch (error) {
                addToConsole(`❌ خطأ في إعادة التهيئة: ${error.message}`, 'error');
            }
        }

        function runQuickDiagnosis() {
            addToConsole('🚀 بدء التشخيص الشامل...');
            
            setTimeout(() => checkNavLinks(), 100);
            setTimeout(() => checkContentSections(), 200);
            setTimeout(() => checkAdminPanel(), 300);
            setTimeout(() => checkLicenseManager(), 400);
            
            setTimeout(() => {
                addToConsole('✅ انتهى التشخيص الشامل');
            }, 500);
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            addToConsole('📋 صفحة التشخيص جاهزة');
            startConsoleMonitoring();
            
            setTimeout(() => {
                runQuickDiagnosis();
            }, 1000);
        });
    </script>
</body>
</html>
