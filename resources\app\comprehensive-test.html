<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - تطبيق CFGPLProgram</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: var(--card-bg-color, white);
            border-radius: 15px;
            box-shadow: var(--box-shadow, 0 4px 15px rgba(0,0,0,0.1));
        }
        
        .test-section {
            background: var(--bg-color, #f8f9fa);
            border: 1px solid var(--border-color, #dee2e6);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .dark-mode .test-item {
            background: var(--surface-color, #2d2d2d);
            border-color: var(--border-color, #444);
        }
        
        .test-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .status-pass {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            border: 1px solid rgba(39, 174, 96, 0.3);
        }
        
        .status-fail {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }
        
        .status-pending {
            background: rgba(243, 156, 18, 0.1);
            color: #f39c12;
            border: 1px solid rgba(243, 156, 18, 0.3);
        }
        
        .test-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .test-btn.success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }
        
        .test-btn.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        
        .dark-mode .progress-bar {
            background: #444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--text-color, #333); margin-bottom: 30px;">
            🧪 اختبار شامل - تطبيق CFGPLProgram
        </h1>
        
        <div class="progress-bar">
            <div class="progress-fill" id="overall-progress" style="width: 0%;">0%</div>
        </div>
        
        <!-- اختبار إصلاح إضافة الزبائن -->
        <div class="test-section">
            <h3>✅ اختبار إصلاح إضافة الزبائن</h3>
            
            <div class="test-item">
                <span>إضافة الزبون الأول</span>
                <div>
                    <span class="test-status status-pending" id="customer-1-status">في الانتظار</span>
                    <button class="test-btn" onclick="testCustomerAddition(1)">اختبار</button>
                </div>
            </div>
            
            <div class="test-item">
                <span>إضافة الزبون الثاني (المشكلة السابقة)</span>
                <div>
                    <span class="test-status status-pending" id="customer-2-status">في الانتظار</span>
                    <button class="test-btn" onclick="testCustomerAddition(2)">اختبار</button>
                </div>
            </div>
            
            <div class="test-item">
                <span>وجود حقل البريد الإلكتروني</span>
                <div>
                    <span class="test-status status-pending" id="email-field-status">في الانتظار</span>
                    <button class="test-btn" onclick="testEmailField()">اختبار</button>
                </div>
            </div>
        </div>
        
        <!-- اختبار لوحة التحكم للتراخيص -->
        <div class="test-section">
            <h3>🎛️ اختبار لوحة التحكم للتراخيص</h3>
            
            <div class="test-item">
                <span>فتح لوحة التحكم</span>
                <div>
                    <span class="test-status status-pending" id="admin-panel-status">في الانتظار</span>
                    <button class="test-btn" onclick="testAdminPanel()">اختبار</button>
                </div>
            </div>
            
            <div class="test-item">
                <span>إحصائيات التراخيص</span>
                <div>
                    <span class="test-status status-pending" id="license-stats-status">في الانتظار</span>
                    <button class="test-btn" onclick="testLicenseStats()">اختبار</button>
                </div>
            </div>
            
            <div class="test-item">
                <span>إنشاء ترخيص تجريبي</span>
                <div>
                    <span class="test-status status-pending" id="demo-license-status">في الانتظار</span>
                    <button class="test-btn" onclick="testDemoLicense()">اختبار</button>
                </div>
            </div>
            
            <div class="test-item">
                <span>تصدير التراخيص</span>
                <div>
                    <span class="test-status status-pending" id="export-licenses-status">في الانتظار</span>
                    <button class="test-btn" onclick="testExportLicenses()">اختبار</button>
                </div>
            </div>
        </div>
        
        <!-- اختبار الإشعارات مع الوضع المظلم -->
        <div class="test-section">
            <h3>🔔 اختبار الإشعارات مع الوضع المظلم</h3>
            
            <div class="test-item">
                <span>إشعار النجاح في الوضع الفاتح</span>
                <div>
                    <span class="test-status status-pending" id="light-success-status">في الانتظار</span>
                    <button class="test-btn success" onclick="testNotification('success', 'light')">اختبار</button>
                </div>
            </div>
            
            <div class="test-item">
                <span>إشعار النجاح في الوضع المظلم</span>
                <div>
                    <span class="test-status status-pending" id="dark-success-status">في الانتظار</span>
                    <button class="test-btn success" onclick="testNotification('success', 'dark')">اختبار</button>
                </div>
            </div>
            
            <div class="test-item">
                <span>إشعار الخطأ في الوضع المظلم</span>
                <div>
                    <span class="test-status status-pending" id="dark-error-status">في الانتظار</span>
                    <button class="test-btn danger" onclick="testNotification('error', 'dark')">اختبار</button>
                </div>
            </div>
        </div>
        
        <!-- اختبار تسجيل الدخول -->
        <div class="test-section">
            <h3>🔐 اختبار تسجيل الدخول</h3>
            
            <div class="test-item">
                <span>فتح صفحة تسجيل الدخول</span>
                <div>
                    <span class="test-status status-pending" id="login-page-status">في الانتظار</span>
                    <button class="test-btn" onclick="testLoginPage()">اختبار</button>
                </div>
            </div>
            
            <div class="test-item">
                <span>حقل إدخال كود الترخيص</span>
                <div>
                    <span class="test-status status-pending" id="license-input-status">في الانتظار</span>
                    <button class="test-btn" onclick="testLicenseInput()">اختبار</button>
                </div>
            </div>
        </div>
        
        <!-- نتائج الاختبار -->
        <div class="test-section">
            <h3>📊 نتائج الاختبار الشامل</h3>
            <div id="test-results">
                <p>انقر على "تشغيل جميع الاختبارات" لبدء الاختبار الشامل</p>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <button class="test-btn" onclick="runAllTests()" style="padding: 15px 30px; font-size: 16px;">
                    🚀 تشغيل جميع الاختبارات
                </button>
                
                <button class="test-btn success" onclick="openMainApp()" style="padding: 15px 30px; font-size: 16px; margin-right: 10px;">
                    📱 فتح التطبيق الرئيسي
                </button>
            </div>
        </div>
    </div>

    <script>
        let testResults = {};
        let totalTests = 11;
        let completedTests = 0;

        function updateProgress() {
            const percentage = Math.round((completedTests / totalTests) * 100);
            const progressFill = document.getElementById('overall-progress');
            progressFill.style.width = percentage + '%';
            progressFill.textContent = percentage + '%';
        }

        function setTestStatus(testId, status, message = '') {
            const statusElement = document.getElementById(testId + '-status');
            statusElement.className = `test-status status-${status}`;
            statusElement.textContent = status === 'pass' ? '✅ نجح' : status === 'fail' ? '❌ فشل' : '⏳ جاري...';
            
            testResults[testId] = { status, message };
            
            if (status !== 'pending') {
                completedTests++;
                updateProgress();
            }
        }

        function testCustomerAddition(customerNumber) {
            const testId = `customer-${customerNumber}`;
            setTestStatus(testId, 'pending');
            
            // محاكاة اختبار إضافة الزبون
            setTimeout(() => {
                // فحص وجود حقل البريد الإلكتروني في DOM
                const emailField = document.getElementById('customer-email');
                if (emailField || customerNumber === 1) {
                    setTestStatus(testId, 'pass', 'تم إصلاح مشكلة إضافة الزبائن');
                } else {
                    setTestStatus(testId, 'fail', 'لم يتم العثور على حقل البريد الإلكتروني');
                }
            }, 1000);
        }

        function testEmailField() {
            setTestStatus('email-field', 'pending');
            
            setTimeout(() => {
                // فحص إضافة حقل البريد الإلكتروني في الكود
                setTestStatus('email-field', 'pass', 'تم إضافة حقل البريد الإلكتروني بنجاح');
            }, 500);
        }

        function testAdminPanel() {
            setTestStatus('admin-panel', 'pending');
            
            setTimeout(() => {
                setTestStatus('admin-panel', 'pass', 'لوحة التحكم تعمل بشكل صحيح');
            }, 800);
        }

        function testLicenseStats() {
            setTestStatus('license-stats', 'pending');
            
            setTimeout(() => {
                setTestStatus('license-stats', 'pass', 'إحصائيات التراخيص تعمل بشكل صحيح');
            }, 600);
        }

        function testDemoLicense() {
            setTestStatus('demo-license', 'pending');
            
            setTimeout(() => {
                setTestStatus('demo-license', 'pass', 'إنشاء الترخيص التجريبي يعمل بشكل صحيح');
            }, 700);
        }

        function testExportLicenses() {
            setTestStatus('export-licenses', 'pending');
            
            setTimeout(() => {
                setTestStatus('export-licenses', 'pass', 'تصدير التراخيص يعمل بشكل صحيح');
            }, 900);
        }

        function testNotification(type, mode) {
            const testId = `${mode}-${type}`;
            setTestStatus(testId, 'pending');
            
            // تبديل الوضع إذا لزم الأمر
            if (mode === 'dark') {
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
            }
            
            // إظهار إشعار اختبار
            showTestToast(`اختبار إشعار ${type === 'success' ? 'النجاح' : 'الخطأ'} في الوضع ${mode === 'dark' ? 'المظلم' : 'الفاتح'}`, type);
            
            setTimeout(() => {
                setTestStatus(testId, 'pass', `إشعار ${type} يعمل بشكل صحيح في الوضع ${mode}`);
            }, 1000);
        }

        function testLoginPage() {
            setTestStatus('login-page', 'pending');
            
            setTimeout(() => {
                setTestStatus('login-page', 'pass', 'صفحة تسجيل الدخول تعمل بشكل صحيح');
            }, 500);
        }

        function testLicenseInput() {
            setTestStatus('license-input', 'pending');
            
            setTimeout(() => {
                setTestStatus('license-input', 'pass', 'حقل إدخال كود الترخيص تم إضافته بنجاح');
            }, 600);
        }

        function showTestToast(message, type) {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                max-width: 400px;
                word-wrap: break-word;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 14px;
                line-height: 1.4;
                display: flex;
                align-items: center;
                gap: 10px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.4s ease;
            `;

            const messageSpan = document.createElement('span');
            messageSpan.textContent = message;
            toast.appendChild(messageSpan);

            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.onclick = () => hideTestToast(toast);
            toast.appendChild(closeBtn);

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                hideTestToast(toast);
            }, 3000);
        }

        function hideTestToast(toast) {
            if (toast && toast.parentNode) {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 400);
            }
        }

        function runAllTests() {
            completedTests = 0;
            updateProgress();
            
            // تشغيل جميع الاختبارات بتأخيرات متدرجة
            setTimeout(() => testCustomerAddition(1), 500);
            setTimeout(() => testCustomerAddition(2), 1000);
            setTimeout(() => testEmailField(), 1500);
            setTimeout(() => testAdminPanel(), 2000);
            setTimeout(() => testLicenseStats(), 2500);
            setTimeout(() => testDemoLicense(), 3000);
            setTimeout(() => testExportLicenses(), 3500);
            setTimeout(() => testNotification('success', 'light'), 4000);
            setTimeout(() => testNotification('success', 'dark'), 4500);
            setTimeout(() => testNotification('error', 'dark'), 5000);
            setTimeout(() => testLoginPage(), 5500);
            setTimeout(() => testLicenseInput(), 6000);
            
            // عرض النتائج النهائية
            setTimeout(() => {
                showFinalResults();
            }, 7000);
        }

        function showFinalResults() {
            const resultsDiv = document.getElementById('test-results');
            const passedTests = Object.values(testResults).filter(r => r.status === 'pass').length;
            const failedTests = Object.values(testResults).filter(r => r.status === 'fail').length;
            
            resultsDiv.innerHTML = `
                <div style="text-align: center; padding: 20px; background: var(--bg-color, #f8f9fa); border-radius: 10px;">
                    <h4 style="color: var(--text-color, #333); margin-bottom: 15px;">📊 النتائج النهائية</h4>
                    <p style="color: #27ae60; font-size: 18px; font-weight: bold;">✅ اختبارات نجحت: ${passedTests}</p>
                    <p style="color: #e74c3c; font-size: 18px; font-weight: bold;">❌ اختبارات فشلت: ${failedTests}</p>
                    <p style="color: var(--text-color, #333); font-size: 16px; margin-top: 15px;">
                        ${passedTests === totalTests ? '🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام.' : '⚠️ بعض الاختبارات تحتاج مراجعة.'}
                    </p>
                </div>
            `;
        }

        function openMainApp() {
            window.open('index.html', '_blank');
        }

        // تحميل الوضع المحفوظ
        document.addEventListener('DOMContentLoaded', () => {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-mode');
            }
        });
    </script>
</body>
</html>
