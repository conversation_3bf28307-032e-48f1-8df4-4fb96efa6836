// إصلاحات خاصة بـ Electron
console.log('🔧 تحميل إصلاحات Electron...');

// فحص إذا كان التطبيق يعمل في Electron
const isElectron = () => {
    return typeof window !== 'undefined' && 
           typeof window.electronAPI !== 'undefined';
};

// إصلاح نظام الإشعارات لـ Electron
function fixElectronNotifications() {
    if (!isElectron()) return;

    console.log('🔔 إصلاح نظام الإشعارات لـ Electron...');

    // إعادة تعريف دالة showToast للعمل مع Electron
    window.showToast = function(message, type = 'info', duration = 5000) {
        console.log(`📢 إشعار ${type}: ${message}`);

        // إنشاء عنصر الإشعار
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        // تطبيق الأنماط مباشرة
        const styles = {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '10px',
            zIndex: '10000',
            maxWidth: '400px',
            wordWrap: 'break-word',
            fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
            fontSize: '14px',
            lineHeight: '1.4',
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.4s ease',
            boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
        };

        // ألوان حسب النوع
        const colors = {
            success: {
                background: 'linear-gradient(135deg, #1e8449, #239b56)',
                color: 'white',
                border: '1px solid rgba(30, 132, 73, 0.3)'
            },
            error: {
                background: 'linear-gradient(135deg, #a93226, #cb4335)',
                color: 'white',
                border: '1px solid rgba(169, 50, 38, 0.3)'
            },
            warning: {
                background: 'linear-gradient(135deg, #b7950b, #d68910)',
                color: 'white',
                border: '1px solid rgba(183, 149, 11, 0.3)'
            },
            info: {
                background: 'linear-gradient(135deg, #1f618d, #2874a6)',
                color: 'white',
                border: '1px solid rgba(31, 97, 141, 0.3)'
            }
        };

        // تطبيق الأنماط
        Object.assign(toast.style, styles);
        Object.assign(toast.style, colors[type] || colors.info);

        // إضافة المحتوى
        const messageSpan = document.createElement('span');
        messageSpan.textContent = message;
        toast.appendChild(messageSpan);

        // زر الإغلاق
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            background: none;
            border: none;
            color: inherit;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            margin-left: 10px;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        `;
        closeBtn.onmouseover = () => closeBtn.style.opacity = '1';
        closeBtn.onmouseout = () => closeBtn.style.opacity = '0.7';
        closeBtn.onclick = () => hideToast(toast);
        toast.appendChild(closeBtn);

        // إضافة إلى الصفحة
        document.body.appendChild(toast);

        // إظهار الإشعار
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 100);

        // إخفاء تلقائي
        setTimeout(() => {
            hideToast(toast);
        }, duration);

        // إشعار نظام Electron
        if (window.electronAPI && window.electronAPI.showNotification) {
            window.electronAPI.showNotification(
                'مؤسسة وقود المستقبل',
                message,
                'assets/icons/app-icon.png'
            );
        }

        return toast;
    };

    function hideToast(toast) {
        if (toast && toast.parentNode) {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 400);
        }
    }

    console.log('✅ تم إصلاح نظام الإشعارات لـ Electron');
}

// إصلاح حفظ وتحميل البيانات
function fixElectronDataManagement() {
    if (!isElectron()) return;

    console.log('💾 إصلاح إدارة البيانات لـ Electron...');

    // إعادة تعريف دوال حفظ وتحميل البيانات
    window.saveData = function(key, data) {
        try {
            if (window.electronAPI) {
                switch (key) {
                    case 'customers':
                        return window.electronAPI.saveCustomerData(data);
                    case 'licenses':
                        return window.electronAPI.saveLicenseData(data);
                    case 'statistics':
                        return window.electronAPI.saveStatistics(data);
                    case 'settings':
                        return window.electronAPI.saveSettings(data);
                    default:
                        return window.electronAPI.saveData(data);
                }
            }
            return false;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    };

    window.loadData = function(key) {
        try {
            if (window.electronAPI) {
                switch (key) {
                    case 'customers':
                        return window.electronAPI.loadCustomerData();
                    case 'licenses':
                        return window.electronAPI.loadLicenseData();
                    case 'statistics':
                        return window.electronAPI.loadStatistics();
                    case 'settings':
                        return window.electronAPI.loadSettings();
                    default:
                        return window.electronAPI.loadData();
                }
            }
            return null;
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            return null;
        }
    };

    console.log('✅ تم إصلاح إدارة البيانات لـ Electron');
}

// إصلاح الجداول والإحصائيات
function fixElectronTablesAndStats() {
    if (!isElectron()) return;

    console.log('📊 إصلاح الجداول والإحصائيات لـ Electron...');

    // تحسين عرض الجداول
    window.updateCustomersTable = function() {
        const customers = loadData('customers') || [];
        const tableBody = document.querySelector('#customers-table tbody');
        
        if (!tableBody) return;

        tableBody.innerHTML = '';

        if (customers.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: #666;">لا توجد بيانات زبائن</td></tr>';
            return;
        }

        customers.forEach((customer, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${customer.name || 'غير محدد'}</td>
                <td>${customer.phone || 'غير محدد'}</td>
                <td>${customer.email || 'غير محدد'}</td>
                <td>${customer.address || 'غير محدد'}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editCustomer(${index})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteCustomer(${index})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        console.log(`✅ تم تحديث جدول الزبائن (${customers.length} زبون)`);
    };

    // تحسين الإحصائيات
    window.updateStatistics = function() {
        const customers = loadData('customers') || [];
        const licenses = loadData('licenses') || [];
        const stats = loadData('statistics') || {};

        // تحديث الإحصائيات الأساسية
        const totalCustomers = customers.length;
        const totalLicenses = licenses.length;
        const activeLicenses = licenses.filter(l => l.status === 'active').length;
        const expiredLicenses = licenses.filter(l => l.status === 'expired').length;

        // تحديث عناصر الإحصائيات في الواجهة
        const updateStatElement = (id, value) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        };

        updateStatElement('total-customers', totalCustomers);
        updateStatElement('total-licenses', totalLicenses);
        updateStatElement('active-licenses', activeLicenses);
        updateStatElement('expired-licenses', expiredLicenses);

        // حفظ الإحصائيات المحدثة
        const updatedStats = {
            ...stats,
            totalCustomers,
            totalLicenses,
            activeLicenses,
            expiredLicenses,
            lastUpdated: new Date().toISOString()
        };

        saveData('statistics', updatedStats);

        console.log('✅ تم تحديث الإحصائيات');
        return updatedStats;
    };

    console.log('✅ تم إصلاح الجداول والإحصائيات لـ Electron');
}

// إصلاح الوضع المظلم
function fixElectronDarkMode() {
    if (!isElectron()) return;

    console.log('🌙 إصلاح الوضع المظلم لـ Electron...');

    // تحسين تبديل الوضع المظلم
    window.toggleDarkMode = function() {
        const body = document.body;
        const isDark = body.classList.contains('dark-mode');
        
        if (isDark) {
            body.classList.remove('dark-mode');
            localStorage.setItem('theme', 'light');
            if (window.electronAPI && window.electronAPI.setTheme) {
                window.electronAPI.setTheme('light');
            }
        } else {
            body.classList.add('dark-mode');
            localStorage.setItem('theme', 'dark');
            if (window.electronAPI && window.electronAPI.setTheme) {
                window.electronAPI.setTheme('dark');
            }
        }

        // تحديث أيقونة الوضع المظلم
        const darkModeBtn = document.getElementById('dark-mode-toggle');
        if (darkModeBtn) {
            const icon = darkModeBtn.querySelector('i');
            if (icon) {
                icon.className = isDark ? 'fas fa-moon' : 'fas fa-sun';
            }
        }

        showToast(isDark ? 'تم تفعيل الوضع الفاتح' : 'تم تفعيل الوضع المظلم', 'success');
    };

    // تحميل الوضع المحفوظ
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-mode');
        if (window.electronAPI && window.electronAPI.setTheme) {
            window.electronAPI.setTheme('dark');
        }
    }

    console.log('✅ تم إصلاح الوضع المظلم لـ Electron');
}

// إصلاح فحص الاتصال
function fixElectronConnectionCheck() {
    if (!isElectron()) return;

    console.log('🌐 إصلاح فحص الاتصال لـ Electron...');

    window.updateConnectionStatus = function() {
        const isOnline = window.electronAPI ? window.electronAPI.checkConnection() : navigator.onLine;
        const statusElement = document.getElementById('connection-status');
        
        if (statusElement) {
            if (isOnline) {
                statusElement.innerHTML = '<i class="fas fa-wifi"></i> متصل';
                statusElement.className = 'connection-status online';
            } else {
                statusElement.innerHTML = '<i class="fas fa-wifi-slash"></i> غير متصل';
                statusElement.className = 'connection-status offline';
            }
        }

        return isOnline;
    };

    // فحص دوري للاتصال
    setInterval(updateConnectionStatus, 5000);
    updateConnectionStatus();

    console.log('✅ تم إصلاح فحص الاتصال لـ Electron');
}

// تشغيل جميع الإصلاحات
function initElectronFixes() {
    if (!isElectron()) {
        console.log('ℹ️ التطبيق لا يعمل في Electron، تخطي الإصلاحات');
        return;
    }

    console.log('🚀 بدء تطبيق إصلاحات Electron...');

    try {
        fixElectronNotifications();
        fixElectronDataManagement();
        fixElectronTablesAndStats();
        fixElectronDarkMode();
        fixElectronConnectionCheck();

        console.log('✅ تم تطبيق جميع إصلاحات Electron بنجاح');
        
        // إشعار نجاح التحميل
        setTimeout(() => {
            showToast('تم تحميل التطبيق بنجاح في Electron', 'success');
        }, 1000);

    } catch (error) {
        console.error('❌ خطأ في تطبيق إصلاحات Electron:', error);
        setTimeout(() => {
            showToast('حدث خطأ في تحميل بعض الميزات', 'warning');
        }, 1000);
    }
}

// تشغيل الإصلاحات عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initElectronFixes);
} else {
    initElectronFixes();
}

// تصدير الدوال للاستخدام العام
window.electronFixes = {
    isElectron,
    initElectronFixes,
    fixElectronNotifications,
    fixElectronDataManagement,
    fixElectronTablesAndStats,
    fixElectronDarkMode,
    fixElectronConnectionCheck
};

console.log('📋 ملف إصلاحات Electron جاهز');
