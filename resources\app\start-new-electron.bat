@echo off
echo ========================================
echo    تشغيل تطبيق Electron الجديد
echo    CFGPLProgram v2.3.0
echo ========================================
echo.

echo 🔧 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    pause
    exit /b 1
)

echo ✅ Node.js متاح

echo.
echo 🔧 التحقق من npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
)

echo ✅ npm متاح

echo.
echo 📦 التحقق من التبعيات...
if not exist "node_modules" (
    echo 🔄 تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات
) else (
    echo ✅ التبعيات موجودة
)

echo.
echo 🚀 تشغيل تطبيق Electron الجديد...
echo.

rem تعديل package.json مؤقتاً لاستخدام main-new.js
powershell -Command "(Get-Content package.json) -replace '\"main\": \"main.js\"', '\"main\": \"main-new.js\"' | Set-Content package.json"

rem تعديل preload في main-new.js لاستخدام preload-new.js
powershell -Command "(Get-Content main-new.js) -replace 'preload.js', 'preload-new.js' | Set-Content main-new.js"

echo ⚡ بدء التطبيق...
npm start

echo.
echo 🔄 إعادة تعيين الملفات...
rem إعادة تعيين package.json
powershell -Command "(Get-Content package.json) -replace '\"main\": \"main-new.js\"', '\"main\": \"main.js\"' | Set-Content package.json"

rem إعادة تعيين main-new.js
powershell -Command "(Get-Content main-new.js) -replace 'preload-new.js', 'preload.js' | Set-Content main-new.js"

echo.
echo 📋 انتهى تشغيل التطبيق
pause
