<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإشعارات مع الوضع المظلم</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--card-bg-color, white);
            border-radius: 15px;
            padding: 30px;
            box-shadow: var(--box-shadow, 0 4px 15px rgba(0,0,0,0.1));
            transition: all 0.3s ease;
        }
        
        h1 {
            color: var(--text-color, #333);
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: var(--bg-color, #f8f9fa);
            border: 1px solid var(--border-color, #dee2e6);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-section h3 {
            color: var(--text-color, #333);
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .btn.success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }
        
        .btn.error {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .btn.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .btn.dark-toggle {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            border: 1px solid rgba(39, 174, 96, 0.3);
        }
        
        .status.info {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }
        
        /* الوضع المظلم */
        .dark-mode .btn.dark-toggle {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .dark-mode .status.success {
            background: rgba(39, 174, 96, 0.2);
            color: #2ecc71;
            border-color: rgba(39, 174, 96, 0.5);
        }
        
        .dark-mode .status.info {
            background: rgba(52, 152, 219, 0.2);
            color: #5dade2;
            border-color: rgba(52, 152, 219, 0.5);
        }
    </style>
</head>
<body>
    <button class="btn dark-toggle" id="dark-toggle">
        <i class="fas fa-moon"></i>
        تبديل الوضع
    </button>

    <div class="container">
        <h1>🔔 اختبار الإشعارات مع الوضع المظلم</h1>
        
        <div class="test-section">
            <h3>✅ الإصلاحات المطبقة:</h3>
            <div class="status success">
                ✅ تم إصلاح ألوان الإشعارات للوضع المظلم
            </div>
            <div class="status success">
                ✅ تم تحديث جميع أنواع الإشعارات (نجاح، خطأ، تحذير، معلومات)
            </div>
            <div class="status success">
                ✅ تم إضافة متغيرات CSS للتوافق مع الوضع المظلم
            </div>
            <div class="status info">
                ℹ️ الإشعارات الآن تستخدم gradients محسنة للوضع المظلم
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 اختبار الإشعارات:</h3>
            <p>انقر على الأزرار التالية لاختبار الإشعارات في الوضع الحالي:</p>
            
            <button class="btn success" onclick="showTestToast('تم الحفظ بنجاح! ✅', 'success')">
                إشعار نجاح
            </button>
            
            <button class="btn error" onclick="showTestToast('حدث خطأ في العملية! ❌', 'error')">
                إشعار خطأ
            </button>
            
            <button class="btn warning" onclick="showTestToast('تحذير: يرجى المراجعة! ⚠️', 'warning')">
                إشعار تحذير
            </button>
            
            <button class="btn" onclick="showTestToast('معلومات مفيدة للمستخدم ℹ️', 'info')">
                إشعار معلومات
            </button>
        </div>

        <div class="test-section">
            <h3>🌙 اختبار الوضع المظلم:</h3>
            <p>1. انقر على زر "تبديل الوضع" في الأعلى</p>
            <p>2. جرب الإشعارات في الوضع المظلم</p>
            <p>3. لاحظ تغيير الألوان والتدرجات</p>
            <p>4. تأكد من وضوح النص والرؤية</p>
        </div>

        <div class="test-section">
            <h3>📋 التحسينات المضافة:</h3>
            <ul style="text-align: right;">
                <li>تدرجات لونية محسنة للوضع المظلم</li>
                <li>ظلال أقوى للوضع المظلم</li>
                <li>حدود شفافة محسنة</li>
                <li>ألوان متباينة للنص</li>
                <li>انتقالات سلسة بين الأوضاع</li>
            </ul>
        </div>
    </div>

    <script>
        // دالة اختبار الإشعارات
        function showTestToast(message, type) {
            // إزالة الإشعارات السابقة
            const existingToasts = document.querySelectorAll('.toast');
            existingToasts.forEach(toast => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            });

            const toast = document.createElement('div');
            toast.className = `toast ${type}`;

            // إضافة المحتوى
            const messageSpan = document.createElement('span');
            messageSpan.textContent = message;
            toast.appendChild(messageSpan);

            // إضافة زر الإغلاق
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.onclick = () => hideTestToast(toast);
            toast.appendChild(closeBtn);

            document.body.appendChild(toast);

            // إظهار الإشعار مع تأثير
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // إخفاء الإشعار تلقائياً بعد 5 ثوان
            setTimeout(() => {
                hideTestToast(toast);
            }, 5000);
        }

        function hideTestToast(toast) {
            if (toast && toast.parentNode) {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 400);
            }
        }

        // تبديل الوضع المظلم
        document.getElementById('dark-toggle').addEventListener('click', () => {
            document.body.classList.toggle('dark-mode');
            const isDark = document.body.classList.contains('dark-mode');
            
            const btn = document.getElementById('dark-toggle');
            if (isDark) {
                btn.innerHTML = '<i class="fas fa-sun"></i> الوضع الفاتح';
                localStorage.setItem('theme', 'dark');
                showTestToast('تم تفعيل الوضع المظلم 🌙', 'info');
            } else {
                btn.innerHTML = '<i class="fas fa-moon"></i> الوضع المظلم';
                localStorage.setItem('theme', 'light');
                showTestToast('تم تفعيل الوضع الفاتح ☀️', 'info');
            }
        });

        // تحميل الوضع المحفوظ
        document.addEventListener('DOMContentLoaded', () => {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-mode');
                document.getElementById('dark-toggle').innerHTML = '<i class="fas fa-sun"></i> الوضع الفاتح';
            }
            
            // إشعار ترحيبي
            setTimeout(() => {
                showTestToast('مرحباً! جرب الإشعارات في الوضعين الفاتح والمظلم 🎉', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
