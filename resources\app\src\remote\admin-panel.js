// نظام لوحة التحكم عن بُعد
class AdminPanel {
    constructor() {
        this.currentSection = 'dashboard';
        this.clients = [];
        this.licenses = [];
        this.activationRequests = [];
        this.activities = [];
        this.charts = {};
        this.init();
    }

    init() {
        console.log('🔧 تهيئة لوحة التحكم عن بُعد...');
        this.setupEventListeners();
        this.loadData();
        this.updateTime();
        this.initializeCharts();
        this.startRealTimeUpdates();
        
        // تحديث الوقت كل ثانية
        setInterval(() => this.updateTime(), 1000);
        
        // تحديث البيانات كل 30 ثانية
        setInterval(() => this.refreshData(), 30000);
    }

    setupEventListeners() {
        // التنقل في الشريط الجانبي
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.switchSection(section);
            });
        });

        // تبديل الشريط الجانبي
        const sidebarToggle = document.getElementById('sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                document.querySelector('.sidebar').classList.toggle('open');
            });
        }

        // تسجيل الخروج
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }

        // أزرار الإجراءات
        this.setupActionButtons();
        
        // تبويبات الإعدادات
        this.setupSettingsTabs();
        
        // النوافذ المنبثقة
        this.setupModals();
    }

    setupActionButtons() {
        // إنشاء ترخيص جديد
        const createLicenseBtn = document.getElementById('create-license-btn');
        if (createLicenseBtn) {
            createLicenseBtn.addEventListener('click', () => this.showCreateLicenseModal());
        }

        // إضافة عميل
        const addClientBtn = document.getElementById('add-client-btn');
        if (addClientBtn) {
            addClientBtn.addEventListener('click', () => this.showAddClientModal());
        }

        // الموافقة على جميع الطلبات
        const approveAllBtn = document.getElementById('approve-all-btn');
        if (approveAllBtn) {
            approveAllBtn.addEventListener('click', () => this.approveAllRequests());
        }

        // رفض الطلبات المحددة
        const rejectSelectedBtn = document.getElementById('reject-selected-btn');
        if (rejectSelectedBtn) {
            rejectSelectedBtn.addEventListener('click', () => this.rejectSelectedRequests());
        }
    }

    setupSettingsTabs() {
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const tabId = btn.dataset.tab;
                this.switchTab(tabId);
            });
        });
    }

    setupModals() {
        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                btn.closest('.modal').classList.remove('show');
            });
        });

        // إغلاق بالنقر خارج النافذة
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });
    }

    switchSection(sectionName) {
        // إخفاء جميع الأقسام
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // إظهار القسم المحدد
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // تحديث التنقل
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`).parentElement;
        activeNavItem.classList.add('active');

        // تحديث عنوان الصفحة
        const titles = {
            dashboard: 'لوحة المعلومات',
            licenses: 'إدارة التراخيص',
            clients: 'العملاء',
            activations: 'طلبات التفعيل',
            monitoring: 'المراقبة',
            settings: 'الإعدادات'
        };

        document.getElementById('page-title').textContent = titles[sectionName] || 'لوحة التحكم';
        this.currentSection = sectionName;

        // تحديث البيانات للقسم الجديد
        this.loadSectionData(sectionName);
    }

    switchTab(tabId) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إظهار التبويب المحدد
        const targetTab = document.getElementById(`${tabId}-tab`);
        const targetBtn = document.querySelector(`[data-tab="${tabId}"]`);

        if (targetTab && targetBtn) {
            targetTab.classList.add('active');
            targetBtn.classList.add('active');
        }
    }

    async loadData() {
        try {
            console.log('📊 تحميل البيانات...');
            
            // محاكاة تحميل البيانات من الخادم
            await this.simulateDataLoading();
            
            // تحديث الإحصائيات
            this.updateStatistics();
            
            // تحديث الجداول
            this.updateTables();
            
            // تحديث الأنشطة
            this.updateActivities();
            
            console.log('✅ تم تحميل البيانات بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
            this.showError('فشل في تحميل البيانات');
        }
    }

    async simulateDataLoading() {
        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 1000));

        // بيانات وهمية للاختبار
        this.clients = [
            { id: 1, name: 'أحمد محمد', phone: '**********', state: 'الجزائر', municipality: 'الجزائر الوسطى', status: 'active', joinDate: '2024-01-15' },
            { id: 2, name: 'فاطمة علي', phone: '**********', state: 'وهران', municipality: 'وهران', status: 'active', joinDate: '2024-02-20' },
            { id: 3, name: 'محمد الأمين', phone: '**********', state: 'قسنطينة', municipality: 'قسنطينة', status: 'inactive', joinDate: '2024-03-10' }
        ];

        this.licenses = [
            { id: 'LIC-001', clientName: 'أحمد محمد', createdDate: '2024-01-15', expiryDate: '2025-01-15', status: 'active', type: 'premium' },
            { id: 'LIC-002', clientName: 'فاطمة علي', createdDate: '2024-02-20', expiryDate: '2024-12-20', status: 'expired', type: 'basic' },
            { id: 'LIC-003', clientName: 'محمد الأمين', createdDate: '2024-03-10', expiryDate: '2025-03-10', status: 'active', type: 'enterprise' }
        ];

        this.activationRequests = [
            { id: 1, firstName: 'سارة', lastName: 'بن علي', phone: '**********', state: 'تيزي وزو', municipality: 'تيزي وزو', businessName: 'محطة الأمل', status: 'pending', requestDate: '2024-12-14' },
            { id: 2, firstName: 'يوسف', lastName: 'مرادي', phone: '**********', state: 'بجاية', municipality: 'بجاية', businessName: 'محطة النور', status: 'pending', requestDate: '2024-12-13' }
        ];

        this.activities = [
            { type: 'success', title: 'تم إنشاء ترخيص جديد', time: 'منذ 5 دقائق', icon: 'fa-key' },
            { type: 'info', title: 'طلب تفعيل جديد', time: 'منذ 15 دقيقة', icon: 'fa-user-plus' },
            { type: 'warning', title: 'ترخيص سينتهي قريباً', time: 'منذ 30 دقيقة', icon: 'fa-exclamation-triangle' },
            { type: 'success', title: 'تم تجديد ترخيص', time: 'منذ ساعة', icon: 'fa-refresh' }
        ];
    }

    updateStatistics() {
        // إحصائيات سريعة
        document.getElementById('total-clients').textContent = this.clients.length;
        document.getElementById('active-clients').textContent = this.clients.filter(c => c.status === 'active').length;
        document.getElementById('active-licenses').textContent = this.licenses.filter(l => l.status === 'active').length;
        document.getElementById('valid-licenses').textContent = this.licenses.filter(l => l.status === 'active').length;
        document.getElementById('expired-licenses').textContent = this.licenses.filter(l => l.status === 'expired').length;
        document.getElementById('pending-requests').textContent = this.activationRequests.filter(r => r.status === 'pending').length;
    }

    updateTables() {
        this.updateLicensesTable();
        this.updateClientsGrid();
        this.updateActivationRequests();
    }

    updateLicensesTable() {
        const tbody = document.querySelector('#licenses-table tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        this.licenses.forEach(license => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><input type="checkbox" value="${license.id}"></td>
                <td>${license.id}</td>
                <td>${license.clientName}</td>
                <td>${this.formatDate(license.createdDate)}</td>
                <td>${this.formatDate(license.expiryDate)}</td>
                <td><span class="status-badge ${license.status}">${this.getStatusText(license.status)}</span></td>
                <td>
                    <button class="btn-icon" onclick="adminPanel.editLicense('${license.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon danger" onclick="adminPanel.deleteLicense('${license.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateClientsGrid() {
        const grid = document.getElementById('clients-grid');
        if (!grid) return;

        grid.innerHTML = '';

        this.clients.forEach(client => {
            const card = document.createElement('div');
            card.className = 'client-card';
            card.innerHTML = `
                <div class="client-header">
                    <h4>${client.name}</h4>
                    <span class="status-badge ${client.status}">${this.getStatusText(client.status)}</span>
                </div>
                <div class="client-info">
                    <p><i class="fas fa-phone"></i> ${client.phone}</p>
                    <p><i class="fas fa-map-marker-alt"></i> ${client.state} - ${client.municipality}</p>
                    <p><i class="fas fa-calendar"></i> انضم في ${this.formatDate(client.joinDate)}</p>
                </div>
                <div class="client-actions">
                    <button class="btn primary" onclick="adminPanel.viewClient(${client.id})">عرض</button>
                    <button class="btn secondary" onclick="adminPanel.editClient(${client.id})">تعديل</button>
                </div>
            `;
            grid.appendChild(card);
        });
    }

    updateActivationRequests() {
        const container = document.getElementById('activation-requests');
        if (!container) return;

        container.innerHTML = '';

        this.activationRequests.forEach(request => {
            const card = document.createElement('div');
            card.className = 'request-card';
            card.innerHTML = `
                <div class="request-header">
                    <h4>${request.firstName} ${request.lastName}</h4>
                    <span class="request-date">${this.formatDate(request.requestDate)}</span>
                </div>
                <div class="request-info">
                    <p><i class="fas fa-phone"></i> ${request.phone}</p>
                    <p><i class="fas fa-map-marker-alt"></i> ${request.state} - ${request.municipality}</p>
                    <p><i class="fas fa-building"></i> ${request.businessName || 'غير محدد'}</p>
                </div>
                <div class="request-actions">
                    <button class="btn success" onclick="adminPanel.approveRequest(${request.id})">
                        <i class="fas fa-check"></i> موافقة
                    </button>
                    <button class="btn danger" onclick="adminPanel.rejectRequest(${request.id})">
                        <i class="fas fa-times"></i> رفض
                    </button>
                    <button class="btn secondary" onclick="adminPanel.viewRequest(${request.id})">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                </div>
            `;
            container.appendChild(card);
        });
    }

    updateActivities() {
        const container = document.getElementById('activities-list');
        if (!container) return;

        container.innerHTML = '';

        this.activities.forEach(activity => {
            const item = document.createElement('div');
            item.className = 'activity-item';
            item.innerHTML = `
                <div class="activity-icon ${activity.type}">
                    <i class="fas ${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">${activity.time}</div>
                </div>
            `;
            container.appendChild(item);
        });
    }

    initializeCharts() {
        this.initLicensesChart();
        this.initActivityChart();
    }

    initLicensesChart() {
        const ctx = document.getElementById('licenses-chart');
        if (!ctx) return;

        this.charts.licenses = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'منتهي', 'معلق'],
                datasets: [{
                    data: [
                        this.licenses.filter(l => l.status === 'active').length,
                        this.licenses.filter(l => l.status === 'expired').length,
                        this.licenses.filter(l => l.status === 'suspended').length
                    ],
                    backgroundColor: ['#27ae60', '#e74c3c', '#f39c12']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    initActivityChart() {
        const ctx = document.getElementById('activity-chart');
        if (!ctx) return;

        this.charts.activity = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'],
                datasets: [{
                    label: 'نشاط العملاء',
                    data: [12, 19, 3, 5, 2, 3, 9],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    startRealTimeUpdates() {
        // محاكاة التحديثات المباشرة
        setInterval(() => {
            this.addRealTimeLog();
        }, 5000);
    }

    addRealTimeLog() {
        const logsContainer = document.getElementById('real-time-logs');
        if (!logsContainer) return;

        const logs = [
            '[INFO] تم تسجيل دخول عميل جديد',
            '[SUCCESS] تم إنشاء ترخيص بنجاح',
            '[WARNING] محاولة دخول غير مصرح بها',
            '[INFO] تم تحديث بيانات العميل',
            '[SUCCESS] تم تجديد ترخيص'
        ];

        const randomLog = logs[Math.floor(Math.random() * logs.length)];
        const timestamp = new Date().toLocaleTimeString('ar-DZ');
        
        const logEntry = document.createElement('div');
        logEntry.textContent = `[${timestamp}] ${randomLog}`;
        
        logsContainer.appendChild(logEntry);
        logsContainer.scrollTop = logsContainer.scrollHeight;

        // الاحتفاظ بآخر 50 سجل فقط
        while (logsContainer.children.length > 50) {
            logsContainer.removeChild(logsContainer.firstChild);
        }
    }

    updateTime() {
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            const now = new Date();
            timeElement.textContent = now.toLocaleString('ar-DZ', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
    }

    async refreshData() {
        console.log('🔄 تحديث البيانات...');
        await this.loadData();
    }

    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.updateStatistics();
                break;
            case 'licenses':
                this.updateLicensesTable();
                break;
            case 'clients':
                this.updateClientsGrid();
                break;
            case 'activations':
                this.updateActivationRequests();
                break;
        }
    }

    // دوال الإجراءات
    async approveRequest(requestId) {
        try {
            console.log(`✅ الموافقة على طلب ${requestId}`);
            
            // محاكاة معالجة الطلب
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // إزالة الطلب من القائمة
            this.activationRequests = this.activationRequests.filter(r => r.id !== requestId);
            
            // تحديث العرض
            this.updateActivationRequests();
            this.updateStatistics();
            
            this.showSuccess('تم الموافقة على الطلب بنجاح');
        } catch (error) {
            this.showError('فشل في معالجة الطلب');
        }
    }

    async rejectRequest(requestId) {
        try {
            console.log(`❌ رفض طلب ${requestId}`);
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            this.activationRequests = this.activationRequests.filter(r => r.id !== requestId);
            
            this.updateActivationRequests();
            this.updateStatistics();
            
            this.showSuccess('تم رفض الطلب');
        } catch (error) {
            this.showError('فشل في معالجة الطلب');
        }
    }

    async approveAllRequests() {
        if (this.activationRequests.length === 0) {
            this.showInfo('لا توجد طلبات معلقة');
            return;
        }

        const confirmed = confirm(`هل أنت متأكد من الموافقة على جميع الطلبات (${this.activationRequests.length} طلب)؟`);
        if (!confirmed) return;

        try {
            console.log('✅ الموافقة على جميع الطلبات');
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            this.activationRequests = [];
            
            this.updateActivationRequests();
            this.updateStatistics();
            
            this.showSuccess('تم الموافقة على جميع الطلبات بنجاح');
        } catch (error) {
            this.showError('فشل في معالجة الطلبات');
        }
    }

    showCreateLicenseModal() {
        const modal = document.getElementById('license-modal');
        if (modal) {
            modal.classList.add('show');
        }
    }

    logout() {
        const confirmed = confirm('هل أنت متأكد من تسجيل الخروج؟');
        if (confirmed) {
            console.log('🚪 تسجيل الخروج من لوحة التحكم');
            // في التطبيق الحقيقي، سيتم إعادة التوجيه إلى صفحة تسجيل الدخول
            window.location.href = '../auth/login.html';
        }
    }

    // دوال مساعدة
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-DZ');
    }

    getStatusText(status) {
        const statusTexts = {
            active: 'نشط',
            inactive: 'غير نشط',
            expired: 'منتهي',
            suspended: 'معلق',
            pending: 'معلق'
        };
        return statusTexts[status] || status;
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showInfo(message) {
        this.showToast(message, 'info');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            z-index: 10000;
            animation: slideInLeft 0.3s ease-out;
            max-width: 400px;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutLeft 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 5000);
    }
}

// تهيئة لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new AdminPanel();
});

// إضافة أنماط CSS للتنبيهات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInLeft {
        from { transform: translateX(-100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutLeft {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(-100%); opacity: 0; }
    }
    
    .toast-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-badge.active {
        background: #d4edda;
        color: #155724;
    }
    
    .status-badge.inactive {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-badge.expired {
        background: #fff3cd;
        color: #856404;
    }
    
    .btn-icon {
        background: none;
        border: none;
        padding: 5px;
        cursor: pointer;
        border-radius: 4px;
        margin: 0 2px;
        transition: background 0.2s;
    }
    
    .btn-icon:hover {
        background: rgba(0,0,0,0.1);
    }
    
    .btn-icon.danger:hover {
        background: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }
`;
document.head.appendChild(style);
