// إصلاح فوري للوحة التحكم
console.log('🔧 بدء الإصلاح الفوري للوحة التحكم...');

// دالة إصلاح بسيطة للتنقل
function setupSimpleNavigation() {
    console.log('🔧 إعداد التنقل البسيط...');

    // انتظار تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSimpleNavigation);
    } else {
        initSimpleNavigation();
    }

    function initSimpleNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        const contentSections = document.querySelectorAll('.content-section');

        console.log(`📊 روابط التنقل: ${navLinks.length}, أقسام المحتوى: ${contentSections.length}`);

        if (navLinks.length > 0 && contentSections.length > 0) {
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const sectionName = link.dataset.section;
                    switchToSection(sectionName);
                });
            });
            console.log('✅ تم إعداد التنقل البسيط');
        }
    }

    function switchToSection(sectionName) {
        console.log(`🔄 التبديل إلى: ${sectionName}`);

        // إخفاء جميع الأقسام
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // إظهار القسم المطلوب
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            console.log(`✅ تم إظهار القسم: ${sectionName}`);
        }

        // تحديث التنقل
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        const navLink = document.querySelector(`[data-section="${sectionName}"]`);
        if (navLink && navLink.parentElement) {
            navLink.parentElement.classList.add('active');
        }

        // تحديث العنوان
        const titles = {
            dashboard: 'لوحة المعلومات',
            licenses: 'إدارة التراخيص',
            clients: 'العملاء',
            activations: 'طلبات التفعيل',
            monitoring: 'المراقبة',
            settings: 'الإعدادات'
        };

        const pageTitle = document.getElementById('page-title');
        if (pageTitle) {
            pageTitle.textContent = titles[sectionName] || 'لوحة التحكم';
        }
    }

    // تصدير الدالة للاستخدام العام
    window.switchToSection = switchToSection;
}

// تشغيل الإصلاح فوراً
setupSimpleNavigation();

// نظام لوحة التحكم عن بُعد
class AdminPanel {
    constructor() {
        this.currentSection = 'dashboard';
        this.clients = [];
        this.licenses = [];
        this.activationRequests = [];
        this.activities = [];
        this.charts = {};
        this.selectedLicenses = new Set();

        // تهيئة مدير التراخيص بحذر
        try {
            if (typeof LicenseManager !== 'undefined') {
                this.licenseManager = new LicenseManager();
                console.log('✅ تم تهيئة مدير التراخيص');
            }
        } catch (error) {
            console.warn('⚠️ لم يتم تهيئة مدير التراخيص:', error.message);
        }

        this.init();
    }

    init() {
        console.log('🔧 تهيئة لوحة التحكم عن بُعد...');

        // تهيئة مدير التراخيص
        if (typeof LicenseManager !== 'undefined') {
            this.licenseManager = new LicenseManager();
            console.log('✅ تم تهيئة مدير التراخيص');
        } else {
            console.warn('⚠️ LicenseManager غير متاح');
        }

        this.setupEventListeners();
        this.loadData();
        this.updateTime();
        this.initializeCharts();
        this.startRealTimeUpdates();

        // تحديث الوقت كل ثانية
        setInterval(() => this.updateTime(), 1000);

        // تحديث البيانات كل 30 ثانية
        setInterval(() => this.refreshData(), 30000);

        // تحميل القسم الافتراضي
        setTimeout(() => {
            this.switchSection('dashboard');
        }, 500);
    }

    setupEventListeners() {
        // التنقل في الشريط الجانبي
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.switchSection(section);
            });
        });

        // تبديل الشريط الجانبي
        const sidebarToggle = document.getElementById('sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                document.querySelector('.sidebar').classList.toggle('open');
            });
        }

        // تسجيل الخروج
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }

        // أزرار الإجراءات
        this.setupActionButtons();
        
        // تبويبات الإعدادات
        this.setupSettingsTabs();
        
        // النوافذ المنبثقة
        this.setupModals();
    }

    setupActionButtons() {
        // إنشاء ترخيص جديد
        const createLicenseBtn = document.getElementById('create-license-btn');
        if (createLicenseBtn) {
            createLicenseBtn.addEventListener('click', () => this.showCreateLicenseModal());
        }

        // إضافة عميل
        const addClientBtn = document.getElementById('add-client-btn');
        if (addClientBtn) {
            addClientBtn.addEventListener('click', () => this.showAddClientModal());
        }

        // الموافقة على جميع الطلبات
        const approveAllBtn = document.getElementById('approve-all-btn');
        if (approveAllBtn) {
            approveAllBtn.addEventListener('click', () => this.approveAllRequests());
        }

        // رفض الطلبات المحددة
        const rejectSelectedBtn = document.getElementById('reject-selected-btn');
        if (rejectSelectedBtn) {
            rejectSelectedBtn.addEventListener('click', () => this.rejectSelectedRequests());
        }
    }

    setupSettingsTabs() {
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const tabId = btn.dataset.tab;
                this.switchTab(tabId);
            });
        });
    }

    setupModals() {
        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                btn.closest('.modal').classList.remove('show');
            });
        });

        // إغلاق بالنقر خارج النافذة
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });
    }

    switchSection(sectionName) {
        try {
            console.log(`🔄 التبديل إلى القسم: ${sectionName}`);

            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // إظهار القسم المحدد
            const targetSection = document.getElementById(`${sectionName}-section`);
            if (targetSection) {
                targetSection.classList.add('active');
                console.log(`✅ تم إظهار القسم: ${sectionName}`);
            } else {
                console.error(`❌ لم يتم العثور على القسم: ${sectionName}-section`);
                return;
            }

            // تحديث التنقل
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            const navLink = document.querySelector(`[data-section="${sectionName}"]`);
            if (navLink && navLink.parentElement) {
                navLink.parentElement.classList.add('active');
            }

            // تحديث عنوان الصفحة
            const titles = {
                dashboard: 'لوحة المعلومات',
                licenses: 'إدارة التراخيص',
                clients: 'العملاء',
                activations: 'طلبات التفعيل',
                monitoring: 'المراقبة',
                settings: 'الإعدادات'
            };

            const pageTitle = document.getElementById('page-title');
            if (pageTitle) {
                pageTitle.textContent = titles[sectionName] || 'لوحة التحكم';
            }

            this.currentSection = sectionName;

            // تحديث البيانات للقسم الجديد
            this.loadSectionData(sectionName);

        } catch (error) {
            console.error('❌ خطأ في تبديل القسم:', error);
        }
    }

    switchTab(tabId) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إظهار التبويب المحدد
        const targetTab = document.getElementById(`${tabId}-tab`);
        const targetBtn = document.querySelector(`[data-tab="${tabId}"]`);

        if (targetTab && targetBtn) {
            targetTab.classList.add('active');
            targetBtn.classList.add('active');
        }
    }

    async loadData() {
        try {
            console.log('📊 تحميل البيانات...');
            
            // محاكاة تحميل البيانات من الخادم
            await this.simulateDataLoading();
            
            // تحديث الإحصائيات
            this.updateStatistics();
            
            // تحديث الجداول
            this.updateTables();
            
            // تحديث الأنشطة
            this.updateActivities();
            
            console.log('✅ تم تحميل البيانات بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
            this.showError('فشل في تحميل البيانات');
        }
    }

    async simulateDataLoading() {
        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 1000));

        // بيانات وهمية للاختبار
        this.clients = [
            { id: 1, name: 'أحمد محمد', phone: '**********', state: 'الجزائر', municipality: 'الجزائر الوسطى', status: 'active', joinDate: '2024-01-15' },
            { id: 2, name: 'فاطمة علي', phone: '**********', state: 'وهران', municipality: 'وهران', status: 'active', joinDate: '2024-02-20' },
            { id: 3, name: 'محمد الأمين', phone: '**********', state: 'قسنطينة', municipality: 'قسنطينة', status: 'inactive', joinDate: '2024-03-10' }
        ];

        this.licenses = [
            { id: 'LIC-001', clientName: 'أحمد محمد', createdDate: '2024-01-15', expiryDate: '2025-01-15', status: 'active', type: 'premium' },
            { id: 'LIC-002', clientName: 'فاطمة علي', createdDate: '2024-02-20', expiryDate: '2024-12-20', status: 'expired', type: 'basic' },
            { id: 'LIC-003', clientName: 'محمد الأمين', createdDate: '2024-03-10', expiryDate: '2025-03-10', status: 'active', type: 'enterprise' }
        ];

        this.activationRequests = [
            { id: 1, firstName: 'سارة', lastName: 'بن علي', phone: '**********', state: 'تيزي وزو', municipality: 'تيزي وزو', businessName: 'محطة الأمل', status: 'pending', requestDate: '2024-12-14' },
            { id: 2, firstName: 'يوسف', lastName: 'مرادي', phone: '**********', state: 'بجاية', municipality: 'بجاية', businessName: 'محطة النور', status: 'pending', requestDate: '2024-12-13' }
        ];

        this.activities = [
            { type: 'success', title: 'تم إنشاء ترخيص جديد', time: 'منذ 5 دقائق', icon: 'fa-key' },
            { type: 'info', title: 'طلب تفعيل جديد', time: 'منذ 15 دقيقة', icon: 'fa-user-plus' },
            { type: 'warning', title: 'ترخيص سينتهي قريباً', time: 'منذ 30 دقيقة', icon: 'fa-exclamation-triangle' },
            { type: 'success', title: 'تم تجديد ترخيص', time: 'منذ ساعة', icon: 'fa-refresh' }
        ];
    }

    updateStatistics() {
        // إحصائيات سريعة
        document.getElementById('total-clients').textContent = this.clients.length;
        document.getElementById('active-clients').textContent = this.clients.filter(c => c.status === 'active').length;
        document.getElementById('active-licenses').textContent = this.licenses.filter(l => l.status === 'active').length;
        document.getElementById('valid-licenses').textContent = this.licenses.filter(l => l.status === 'active').length;
        document.getElementById('expired-licenses').textContent = this.licenses.filter(l => l.status === 'expired').length;
        document.getElementById('pending-requests').textContent = this.activationRequests.filter(r => r.status === 'pending').length;
    }

    updateTables() {
        this.updateLicensesTable();
        this.updateClientsGrid();
        this.updateActivationRequests();
    }

    updateLicensesTable() {
        const tbody = document.querySelector('#licenses-table tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        this.licenses.forEach(license => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><input type="checkbox" value="${license.id}"></td>
                <td>${license.id}</td>
                <td>${license.clientName}</td>
                <td>${this.formatDate(license.createdDate)}</td>
                <td>${this.formatDate(license.expiryDate)}</td>
                <td><span class="status-badge ${license.status}">${this.getStatusText(license.status)}</span></td>
                <td>
                    <button class="btn-icon" onclick="adminPanel.editLicense('${license.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon danger" onclick="adminPanel.deleteLicense('${license.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateClientsGrid() {
        const grid = document.getElementById('clients-grid');
        if (!grid) return;

        grid.innerHTML = '';

        this.clients.forEach(client => {
            const card = document.createElement('div');
            card.className = 'client-card';
            card.innerHTML = `
                <div class="client-header">
                    <h4>${client.name}</h4>
                    <span class="status-badge ${client.status}">${this.getStatusText(client.status)}</span>
                </div>
                <div class="client-info">
                    <p><i class="fas fa-phone"></i> ${client.phone}</p>
                    <p><i class="fas fa-map-marker-alt"></i> ${client.state} - ${client.municipality}</p>
                    <p><i class="fas fa-calendar"></i> انضم في ${this.formatDate(client.joinDate)}</p>
                </div>
                <div class="client-actions">
                    <button class="btn primary" onclick="adminPanel.viewClient(${client.id})">عرض</button>
                    <button class="btn secondary" onclick="adminPanel.editClient(${client.id})">تعديل</button>
                </div>
            `;
            grid.appendChild(card);
        });
    }

    updateActivationRequests() {
        const container = document.getElementById('activation-requests');
        if (!container) return;

        container.innerHTML = '';

        this.activationRequests.forEach(request => {
            const card = document.createElement('div');
            card.className = 'request-card';
            card.innerHTML = `
                <div class="request-header">
                    <h4>${request.firstName} ${request.lastName}</h4>
                    <span class="request-date">${this.formatDate(request.requestDate)}</span>
                </div>
                <div class="request-info">
                    <p><i class="fas fa-phone"></i> ${request.phone}</p>
                    <p><i class="fas fa-map-marker-alt"></i> ${request.state} - ${request.municipality}</p>
                    <p><i class="fas fa-building"></i> ${request.businessName || 'غير محدد'}</p>
                </div>
                <div class="request-actions">
                    <button class="btn success" onclick="adminPanel.approveRequest(${request.id})">
                        <i class="fas fa-check"></i> موافقة
                    </button>
                    <button class="btn danger" onclick="adminPanel.rejectRequest(${request.id})">
                        <i class="fas fa-times"></i> رفض
                    </button>
                    <button class="btn secondary" onclick="adminPanel.viewRequest(${request.id})">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                </div>
            `;
            container.appendChild(card);
        });
    }

    updateActivities() {
        const container = document.getElementById('activities-list');
        if (!container) return;

        container.innerHTML = '';

        this.activities.forEach(activity => {
            const item = document.createElement('div');
            item.className = 'activity-item';
            item.innerHTML = `
                <div class="activity-icon ${activity.type}">
                    <i class="fas ${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">${activity.time}</div>
                </div>
            `;
            container.appendChild(item);
        });
    }

    initializeCharts() {
        this.initLicensesChart();
        this.initActivityChart();
    }

    initLicensesChart() {
        const ctx = document.getElementById('licenses-chart');
        if (!ctx) return;

        this.charts.licenses = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'منتهي', 'معلق'],
                datasets: [{
                    data: [
                        this.licenses.filter(l => l.status === 'active').length,
                        this.licenses.filter(l => l.status === 'expired').length,
                        this.licenses.filter(l => l.status === 'suspended').length
                    ],
                    backgroundColor: ['#27ae60', '#e74c3c', '#f39c12']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    initActivityChart() {
        const ctx = document.getElementById('activity-chart');
        if (!ctx) return;

        this.charts.activity = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'],
                datasets: [{
                    label: 'نشاط العملاء',
                    data: [12, 19, 3, 5, 2, 3, 9],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    startRealTimeUpdates() {
        // محاكاة التحديثات المباشرة
        setInterval(() => {
            this.addRealTimeLog();
        }, 5000);
    }

    addRealTimeLog() {
        const logsContainer = document.getElementById('real-time-logs');
        if (!logsContainer) return;

        const logs = [
            '[INFO] تم تسجيل دخول عميل جديد',
            '[SUCCESS] تم إنشاء ترخيص بنجاح',
            '[WARNING] محاولة دخول غير مصرح بها',
            '[INFO] تم تحديث بيانات العميل',
            '[SUCCESS] تم تجديد ترخيص'
        ];

        const randomLog = logs[Math.floor(Math.random() * logs.length)];
        const timestamp = new Date().toLocaleTimeString('ar-DZ');
        
        const logEntry = document.createElement('div');
        logEntry.textContent = `[${timestamp}] ${randomLog}`;
        
        logsContainer.appendChild(logEntry);
        logsContainer.scrollTop = logsContainer.scrollHeight;

        // الاحتفاظ بآخر 50 سجل فقط
        while (logsContainer.children.length > 50) {
            logsContainer.removeChild(logsContainer.firstChild);
        }
    }

    updateTime() {
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            const now = new Date();
            timeElement.textContent = now.toLocaleString('ar-DZ', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
    }

    async refreshData() {
        console.log('🔄 تحديث البيانات...');
        await this.loadData();
    }

    loadSectionData(sectionName) {
        console.log(`📂 تحميل بيانات القسم: ${sectionName}`);
        switch (sectionName) {
            case 'dashboard':
                this.updateStatistics();
                break;
            case 'licenses':
                if (this.licenseManager) {
                    this.licenseManager.loadLicenses();
                    this.licenseManager.updateLicenseStats();
                }
                break;
            case 'clients':
                this.updateClientsGrid();
                break;
            case 'activations':
                this.updateActivationRequests();
                break;
            case 'monitoring':
                this.updateMonitoringData();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }

    // دوال تحديث البيانات للأقسام المختلفة
    updateClientsGrid() {
        console.log('👥 تحديث شبكة العملاء...');
        // تحديث جدول العملاء
        const clientsTable = document.querySelector('#clients-table tbody');
        if (clientsTable) {
            clientsTable.innerHTML = '<tr><td colspan="5">جاري تحميل بيانات العملاء...</td></tr>';
        }
    }

    updateActivationRequests() {
        console.log('📋 تحديث طلبات التفعيل...');
        // تحديث طلبات التفعيل
        const requestsTable = document.querySelector('#activation-requests-table tbody');
        if (requestsTable) {
            requestsTable.innerHTML = '<tr><td colspan="6">جاري تحميل طلبات التفعيل...</td></tr>';
        }
    }

    updateMonitoringData() {
        console.log('📊 تحديث بيانات المراقبة...');
        // تحديث بيانات المراقبة
    }

    loadSettings() {
        console.log('⚙️ تحميل الإعدادات...');
        // تحميل إعدادات النظام
    }

    // دوال الإجراءات
    async approveRequest(requestId) {
        try {
            console.log(`✅ الموافقة على طلب ${requestId}`);
            
            // محاكاة معالجة الطلب
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // إزالة الطلب من القائمة
            this.activationRequests = this.activationRequests.filter(r => r.id !== requestId);
            
            // تحديث العرض
            this.updateActivationRequests();
            this.updateStatistics();
            
            this.showSuccess('تم الموافقة على الطلب بنجاح');
        } catch (error) {
            this.showError('فشل في معالجة الطلب');
        }
    }

    async rejectRequest(requestId) {
        try {
            console.log(`❌ رفض طلب ${requestId}`);
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            this.activationRequests = this.activationRequests.filter(r => r.id !== requestId);
            
            this.updateActivationRequests();
            this.updateStatistics();
            
            this.showSuccess('تم رفض الطلب');
        } catch (error) {
            this.showError('فشل في معالجة الطلب');
        }
    }

    async approveAllRequests() {
        if (this.activationRequests.length === 0) {
            this.showInfo('لا توجد طلبات معلقة');
            return;
        }

        const confirmed = confirm(`هل أنت متأكد من الموافقة على جميع الطلبات (${this.activationRequests.length} طلب)؟`);
        if (!confirmed) return;

        try {
            console.log('✅ الموافقة على جميع الطلبات');
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            this.activationRequests = [];
            
            this.updateActivationRequests();
            this.updateStatistics();
            
            this.showSuccess('تم الموافقة على جميع الطلبات بنجاح');
        } catch (error) {
            this.showError('فشل في معالجة الطلبات');
        }
    }

    showCreateLicenseModal() {
        const modal = document.getElementById('license-modal');
        if (modal) {
            modal.classList.add('show');
        }
    }

    logout() {
        const confirmed = confirm('هل أنت متأكد من تسجيل الخروج؟');
        if (confirmed) {
            console.log('🚪 تسجيل الخروج من لوحة التحكم');
            // في التطبيق الحقيقي، سيتم إعادة التوجيه إلى صفحة تسجيل الدخول
            window.location.href = '../auth/login.html';
        }
    }

    // دوال مساعدة
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-DZ');
    }

    getStatusText(status) {
        const statusTexts = {
            active: 'نشط',
            inactive: 'غير نشط',
            expired: 'منتهي',
            suspended: 'معلق',
            pending: 'معلق'
        };
        return statusTexts[status] || status;
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showInfo(message) {
        this.showToast(message, 'info');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            z-index: 10000;
            animation: slideInLeft 0.3s ease-out;
            max-width: 400px;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutLeft 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 5000);
    }
}

// تهيئة لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 بدء تهيئة لوحة التحكم...');

    try {
        // التحقق من وجود العناصر المطلوبة
        const navLinks = document.querySelectorAll('.nav-link');
        const contentSections = document.querySelectorAll('.content-section');

        console.log(`📊 عدد روابط التنقل: ${navLinks.length}`);
        console.log(`📊 عدد أقسام المحتوى: ${contentSections.length}`);

        if (navLinks.length === 0) {
            console.error('❌ لم يتم العثور على روابط التنقل');
            return;
        }

        if (contentSections.length === 0) {
            console.error('❌ لم يتم العثور على أقسام المحتوى');
            return;
        }

        // إنشاء كائن لوحة التحكم
        window.adminPanel = new AdminPanel();
        console.log('✅ تم تهيئة لوحة التحكم بنجاح');

        // إضافة مستمعي الأحداث للتنقل
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                console.log(`🔄 النقر على رابط: ${section}`);

                if (window.adminPanel && typeof window.adminPanel.switchSection === 'function') {
                    window.adminPanel.switchSection(section);
                } else {
                    console.error('❌ دالة switchSection غير متاحة');
                }
            });
        });

        console.log('✅ تم ربط أحداث التنقل');

    } catch (error) {
        console.error('❌ خطأ في تهيئة لوحة التحكم:', error);
    }
});

// إضافة أنماط CSS للتنبيهات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInLeft {
        from { transform: translateX(-100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutLeft {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(-100%); opacity: 0; }
    }
    
    .toast-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-badge.active {
        background: #d4edda;
        color: #155724;
    }
    
    .status-badge.inactive {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-badge.expired {
        background: #fff3cd;
        color: #856404;
    }
    
    .btn-icon {
        background: none;
        border: none;
        padding: 5px;
        cursor: pointer;
        border-radius: 4px;
        margin: 0 2px;
        transition: background 0.2s;
    }
    
    .btn-icon:hover {
        background: rgba(0,0,0,0.1);
    }
    
    .btn-icon.danger:hover {
        background: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }
`;
document.head.appendChild(style);

// فئة إدارة التراخيص المتقدمة
class LicenseManager {
    constructor() {
        this.licenses = [];
        this.filteredLicenses = [];
        this.currentFilter = { status: 'all', type: 'all', search: '' };
        this.init();
    }

    init() {
        this.loadLicenses();
        this.setupEventListeners();
        this.loadStatesData();
    }

    setupEventListeners() {
        // أزرار الإجراءات الرئيسية
        document.getElementById('create-license-btn')?.addEventListener('click', () => this.showCreateLicenseModal());
        document.getElementById('generate-demo-license-btn')?.addEventListener('click', () => this.generateDemoLicense());
        document.getElementById('export-licenses-btn')?.addEventListener('click', () => this.exportLicenses());
        document.getElementById('refresh-licenses-btn')?.addEventListener('click', () => this.refreshLicenses());

        // الفلاتر
        document.getElementById('license-status-filter')?.addEventListener('change', (e) => this.filterLicenses());
        document.getElementById('license-type-filter')?.addEventListener('change', (e) => this.filterLicenses());
        document.getElementById('license-search')?.addEventListener('input', (e) => this.filterLicenses());

        // تحديد الكل
        document.getElementById('select-all-licenses')?.addEventListener('change', (e) => this.toggleSelectAll(e.target.checked));

        // النوافذ المنبثقة
        this.setupModalEventListeners();

        // الإجراءات المتعددة
        this.setupBulkActions();
    }

    setupModalEventListeners() {
        // نافذة إنشاء ترخيص
        document.getElementById('close-create-license-modal')?.addEventListener('click', () => this.hideCreateLicenseModal());
        document.getElementById('cancel-create-license')?.addEventListener('click', () => this.hideCreateLicenseModal());
        document.getElementById('create-license-form')?.addEventListener('submit', (e) => this.handleCreateLicense(e));

        // نافذة تفاصيل الترخيص
        document.getElementById('close-license-details-modal')?.addEventListener('click', () => this.hideLicenseDetailsModal());

        // نافذة تعديل الترخيص
        document.getElementById('close-edit-license-modal')?.addEventListener('click', () => this.hideEditLicenseModal());
        document.getElementById('cancel-edit-license')?.addEventListener('click', () => this.hideEditLicenseModal());
        document.getElementById('edit-license-form')?.addEventListener('submit', (e) => this.handleEditLicense(e));

        // نافذة حذف الترخيص
        document.getElementById('close-delete-license-modal')?.addEventListener('click', () => this.hideDeleteLicenseModal());
        document.getElementById('cancel-delete-license')?.addEventListener('click', () => this.hideDeleteLicenseModal());
        document.getElementById('confirm-delete-license')?.addEventListener('click', () => this.confirmDeleteLicense());

        // إغلاق النوافذ بالنقر خارجها
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // تحديث البلديات عند تغيير الولاية
        document.getElementById('client-state')?.addEventListener('change', () => this.updateMunicipalities());
    }

    setupBulkActions() {
        document.getElementById('bulk-activate-btn')?.addEventListener('click', () => this.bulkActivate());
        document.getElementById('bulk-suspend-btn')?.addEventListener('click', () => this.bulkSuspend());
        document.getElementById('bulk-delete-btn')?.addEventListener('click', () => this.bulkDelete());
        document.getElementById('bulk-export-btn')?.addEventListener('click', () => this.bulkExport());
    }

    loadLicenses() {
        // تحميل التراخيص من localStorage أو إنشاء بيانات تجريبية
        const storedLicenses = localStorage.getItem('adminLicenses');
        if (storedLicenses) {
            this.licenses = JSON.parse(storedLicenses);
        } else {
            this.licenses = this.generateSampleLicenses();
            this.saveLicenses();
        }

        this.filteredLicenses = [...this.licenses];
        this.renderLicensesTable();
        this.updateLicenseStats();
    }

    generateSampleLicenses() {
        const sampleLicenses = [];
        const types = ['demo', 'basic', 'premium', 'enterprise'];
        const statuses = ['active', 'expired', 'suspended'];
        const names = [
            { first: 'أحمد', last: 'محمد' },
            { first: 'فاطمة', last: 'علي' },
            { first: 'محمد', last: 'حسن' },
            { first: 'عائشة', last: 'إبراهيم' },
            { first: 'عبدالله', last: 'أحمد' }
        ];

        for (let i = 0; i < 15; i++) {
            const name = names[i % names.length];
            const type = types[Math.floor(Math.random() * types.length)];
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const createdDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
            const expiryDate = new Date(createdDate.getTime() + (type === 'demo' ? 30 : 365) * 24 * 60 * 60 * 1000);

            sampleLicenses.push({
                id: `LIC-${Date.now()}-${i}`,
                key: `FF-${type.toUpperCase().substr(0, 4)}-${String(2024)}-${String(i + 1).padStart(3, '0')}`,
                type: type,
                status: status,
                clientInfo: {
                    firstName: name.first,
                    lastName: name.last,
                    phone: `069692417${i % 10}`,
                    email: `${name.first.toLowerCase()}@example.com`,
                    state: 'الجزائر',
                    municipality: 'الجزائر الوسطى',
                    businessName: i % 3 === 0 ? `مؤسسة ${name.last}` : ''
                },
                createdDate: createdDate.toISOString(),
                expiryDate: expiryDate.toISOString(),
                deviceId: `DEV-${Date.now()}-${i}`,
                lastActivity: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
                notes: i % 4 === 0 ? 'ترخيص تجريبي للاختبار' : ''
            });
        }

        return sampleLicenses;
    }

    saveLicenses() {
        localStorage.setItem('adminLicenses', JSON.stringify(this.licenses));
    }

    renderLicensesTable() {
        const tbody = document.querySelector('#licenses-table tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        this.filteredLicenses.forEach(license => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="license-checkbox" data-license-id="${license.id}"
                           ${this.selectedLicenses.has(license.id) ? 'checked' : ''}>
                </td>
                <td>
                    <div class="license-key-cell">
                        <strong>${license.key}</strong>
                        <small>${license.type}</small>
                    </div>
                </td>
                <td>
                    <div class="client-info">
                        <strong>${license.clientInfo.firstName} ${license.clientInfo.lastName}</strong>
                        <small>${license.clientInfo.phone}</small>
                    </div>
                </td>
                <td>
                    <span class="license-type-badge ${license.type}">${this.getTypeText(license.type)}</span>
                </td>
                <td>${this.formatDate(license.createdDate)}</td>
                <td>${this.formatDate(license.expiryDate)}</td>
                <td>
                    <span class="days-remaining ${this.getDaysRemainingClass(license)}">
                        ${this.getDaysRemaining(license)} يوم
                    </span>
                </td>
                <td>
                    <span class="license-status-badge ${license.status}">
                        ${this.getStatusText(license.status)}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view" onclick="licenseManager.viewLicense('${license.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit" onclick="licenseManager.editLicense('${license.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="licenseManager.deleteLicense('${license.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;

            // إضافة مستمع للتحديد
            const checkbox = row.querySelector('.license-checkbox');
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.selectedLicenses.add(license.id);
                } else {
                    this.selectedLicenses.delete(license.id);
                }
                this.updateBulkActionsBar();
            });

            tbody.appendChild(row);
        });

        this.updateBulkActionsBar();
    }

    updateLicenseStats() {
        const stats = {
            active: this.licenses.filter(l => l.status === 'active').length,
            expired: this.licenses.filter(l => l.status === 'expired').length,
            suspended: this.licenses.filter(l => l.status === 'suspended').length,
            expiring: this.licenses.filter(l => this.getDaysRemaining(l) <= 7 && l.status === 'active').length
        };

        document.getElementById('active-licenses-count').textContent = stats.active;
        document.getElementById('expired-licenses-count').textContent = stats.expired;
        document.getElementById('suspended-licenses-count').textContent = stats.suspended;
        document.getElementById('expiring-licenses-count').textContent = stats.expiring;
    }

    filterLicenses() {
        const statusFilter = document.getElementById('license-status-filter').value;
        const typeFilter = document.getElementById('license-type-filter').value;
        const searchTerm = document.getElementById('license-search').value.toLowerCase();

        this.filteredLicenses = this.licenses.filter(license => {
            const matchesStatus = statusFilter === 'all' ||
                                (statusFilter === 'expiring' ? this.getDaysRemaining(license) <= 7 && license.status === 'active' : license.status === statusFilter);
            const matchesType = typeFilter === 'all' || license.type === typeFilter;
            const matchesSearch = !searchTerm ||
                                license.key.toLowerCase().includes(searchTerm) ||
                                `${license.clientInfo.firstName} ${license.clientInfo.lastName}`.toLowerCase().includes(searchTerm) ||
                                license.clientInfo.phone.includes(searchTerm);

            return matchesStatus && matchesType && matchesSearch;
        });

        this.renderLicensesTable();
    }

    getDaysRemaining(license) {
        const now = new Date();
        const expiryDate = new Date(license.expiryDate);
        const diffTime = expiryDate - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return Math.max(0, diffDays);
    }

    getDaysRemainingClass(license) {
        const days = this.getDaysRemaining(license);
        if (days <= 0) return 'expired';
        if (days <= 7) return 'expiring';
        if (days <= 30) return 'warning';
        return 'good';
    }

    getTypeText(type) {
        const types = {
            demo: 'تجريبي',
            basic: 'أساسي',
            premium: 'مميز',
            enterprise: 'مؤسسي'
        };
        return types[type] || type;
    }

    getStatusText(status) {
        const statuses = {
            active: 'نشط',
            expired: 'منتهي',
            suspended: 'معلق'
        };
        return statuses[status] || status;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-DZ', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    toggleSelectAll(checked) {
        this.selectedLicenses.clear();
        if (checked) {
            this.filteredLicenses.forEach(license => {
                this.selectedLicenses.add(license.id);
            });
        }

        document.querySelectorAll('.license-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
        });

        this.updateBulkActionsBar();
    }

    updateBulkActionsBar() {
        const bulkActionsBar = document.getElementById('bulk-actions-bar');
        const selectedCount = document.getElementById('selected-count');

        if (this.selectedLicenses.size > 0) {
            bulkActionsBar.style.display = 'flex';
            selectedCount.textContent = this.selectedLicenses.size;
        } else {
            bulkActionsBar.style.display = 'none';
        }
    }

    // النوافذ المنبثقة
    showCreateLicenseModal() {
        document.getElementById('create-license-modal').style.display = 'flex';
        this.resetCreateLicenseForm();
    }

    hideCreateLicenseModal() {
        document.getElementById('create-license-modal').style.display = 'none';
    }

    showLicenseDetailsModal(license) {
        const modal = document.getElementById('license-details-modal');
        const content = document.getElementById('license-details-content');

        content.innerHTML = `
            <div class="license-detail-item">
                <span class="license-detail-label">رقم الترخيص:</span>
                <span class="license-detail-value">${license.key}</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">نوع الترخيص:</span>
                <span class="license-detail-value">${this.getTypeText(license.type)}</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">الحالة:</span>
                <span class="license-status-badge ${license.status}">${this.getStatusText(license.status)}</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">اسم العميل:</span>
                <span class="license-detail-value">${license.clientInfo.firstName} ${license.clientInfo.lastName}</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">رقم الهاتف:</span>
                <span class="license-detail-value">${license.clientInfo.phone}</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">البريد الإلكتروني:</span>
                <span class="license-detail-value">${license.clientInfo.email || 'غير محدد'}</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">الموقع:</span>
                <span class="license-detail-value">${license.clientInfo.state} - ${license.clientInfo.municipality}</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">اسم المؤسسة:</span>
                <span class="license-detail-value">${license.clientInfo.businessName || 'غير محدد'}</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">تاريخ الإنشاء:</span>
                <span class="license-detail-value">${this.formatDate(license.createdDate)}</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">تاريخ الانتهاء:</span>
                <span class="license-detail-value">${this.formatDate(license.expiryDate)}</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">الأيام المتبقية:</span>
                <span class="license-detail-value">${this.getDaysRemaining(license)} يوم</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">آخر نشاط:</span>
                <span class="license-detail-value">${this.formatDate(license.lastActivity)}</span>
            </div>
            <div class="license-detail-item">
                <span class="license-detail-label">معرف الجهاز:</span>
                <span class="license-detail-value">${license.deviceId}</span>
            </div>
            ${license.notes ? `
            <div class="license-detail-item">
                <span class="license-detail-label">ملاحظات:</span>
                <span class="license-detail-value">${license.notes}</span>
            </div>
            ` : ''}
        `;

        modal.style.display = 'flex';
    }

    hideLicenseDetailsModal() {
        document.getElementById('license-details-modal').style.display = 'none';
    }

    // إجراءات التراخيص
    viewLicense(licenseId) {
        const license = this.licenses.find(l => l.id === licenseId);
        if (license) {
            this.showLicenseDetailsModal(license);
        }
    }

    editLicense(licenseId) {
        const license = this.licenses.find(l => l.id === licenseId);
        if (license) {
            this.showEditLicenseModal(license);
        }
    }

    deleteLicense(licenseId) {
        const license = this.licenses.find(l => l.id === licenseId);
        if (license) {
            this.showDeleteLicenseModal(license);
        }
    }

    showEditLicenseModal(license) {
        const modal = document.getElementById('edit-license-modal');

        // ملء النموذج بالبيانات الحالية
        document.getElementById('edit-license-id').value = license.id;
        document.getElementById('edit-license-status').value = license.status;
        document.getElementById('edit-license-expiry').value = license.expiryDate.split('T')[0];

        modal.style.display = 'flex';
    }

    hideEditLicenseModal() {
        document.getElementById('edit-license-modal').style.display = 'none';
    }

    showDeleteLicenseModal(license) {
        const modal = document.getElementById('delete-license-modal');
        const info = document.getElementById('delete-license-info');

        info.innerHTML = `
            <strong>رقم الترخيص:</strong> ${license.key}<br>
            <strong>العميل:</strong> ${license.clientInfo.firstName} ${license.clientInfo.lastName}<br>
            <strong>النوع:</strong> ${this.getTypeText(license.type)}
        `;

        this.licenseToDelete = license.id;
        modal.style.display = 'flex';
    }

    hideDeleteLicenseModal() {
        document.getElementById('delete-license-modal').style.display = 'none';
        this.licenseToDelete = null;
    }

    confirmDeleteLicense() {
        if (this.licenseToDelete) {
            this.licenses = this.licenses.filter(l => l.id !== this.licenseToDelete);
            this.saveLicenses();
            this.loadLicenses();
            this.hideDeleteLicenseModal();
            this.showSuccess('تم حذف الترخيص بنجاح');
        }
    }

    async handleCreateLicense(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const licenseData = {
            id: `LIC-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            key: this.generateLicenseKey(formData.get('licenseType')),
            type: formData.get('licenseType'),
            status: 'active',
            clientInfo: {
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                phone: formData.get('phone'),
                email: formData.get('email') || '',
                state: formData.get('state'),
                municipality: formData.get('municipality'),
                businessName: formData.get('businessName') || ''
            },
            createdDate: new Date().toISOString(),
            expiryDate: this.calculateExpiryDate(parseInt(formData.get('duration'))),
            deviceId: `DEV-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            lastActivity: new Date().toISOString(),
            notes: formData.get('notes') || ''
        };

        try {
            this.licenses.push(licenseData);
            this.saveLicenses();
            this.loadLicenses();
            this.hideCreateLicenseModal();
            this.showSuccess('تم إنشاء الترخيص بنجاح');
        } catch (error) {
            this.showError('حدث خطأ أثناء إنشاء الترخيص');
        }
    }

    async handleEditLicense(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const licenseId = formData.get('licenseId');
        const license = this.licenses.find(l => l.id === licenseId);

        if (license) {
            license.status = formData.get('status');
            license.expiryDate = new Date(formData.get('expiryDate')).toISOString();

            if (formData.get('editNotes')) {
                license.notes = (license.notes || '') + '\n' + new Date().toLocaleDateString() + ': ' + formData.get('editNotes');
            }

            this.saveLicenses();
            this.loadLicenses();
            this.hideEditLicenseModal();
            this.showSuccess('تم تحديث الترخيص بنجاح');
        }
    }

    generateLicenseKey(type) {
        const prefix = 'FF';
        const typeCode = type.toUpperCase().substr(0, 4);
        const year = new Date().getFullYear();
        const sequence = String(this.licenses.length + 1).padStart(3, '0');

        return `${prefix}-${typeCode}-${year}-${sequence}`;
    }

    calculateExpiryDate(durationDays) {
        const now = new Date();
        const expiryDate = new Date(now.getTime() + (durationDays * 24 * 60 * 60 * 1000));
        return expiryDate.toISOString();
    }

    generateDemoLicense() {
        const demoLicense = {
            id: `LIC-DEMO-${Date.now()}`,
            key: `FF-DEMO-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
            type: 'demo',
            status: 'active',
            clientInfo: {
                firstName: 'مستخدم',
                lastName: 'تجريبي',
                phone: '**********',
                email: '<EMAIL>',
                state: 'الجزائر',
                municipality: 'الجزائر الوسطى',
                businessName: 'ترخيص تجريبي'
            },
            createdDate: new Date().toISOString(),
            expiryDate: this.calculateExpiryDate(30),
            deviceId: `DEV-DEMO-${Date.now()}`,
            lastActivity: new Date().toISOString(),
            notes: 'ترخيص تجريبي تم إنشاؤه تلقائياً'
        };

        this.licenses.push(demoLicense);
        this.saveLicenses();
        this.loadLicenses();
        this.showSuccess(`تم إنشاء ترخيص تجريبي: ${demoLicense.key}`);
    }

    refreshLicenses() {
        this.loadLicenses();
        this.showInfo('تم تحديث قائمة التراخيص');
    }

    exportLicenses() {
        const dataToExport = this.filteredLicenses.map(license => ({
            'رقم الترخيص': license.key,
            'نوع الترخيص': this.getTypeText(license.type),
            'الحالة': this.getStatusText(license.status),
            'اسم العميل': `${license.clientInfo.firstName} ${license.clientInfo.lastName}`,
            'رقم الهاتف': license.clientInfo.phone,
            'البريد الإلكتروني': license.clientInfo.email,
            'الولاية': license.clientInfo.state,
            'البلدية': license.clientInfo.municipality,
            'اسم المؤسسة': license.clientInfo.businessName,
            'تاريخ الإنشاء': this.formatDate(license.createdDate),
            'تاريخ الانتهاء': this.formatDate(license.expiryDate),
            'الأيام المتبقية': this.getDaysRemaining(license),
            'آخر نشاط': this.formatDate(license.lastActivity),
            'ملاحظات': license.notes
        }));

        const csv = this.convertToCSV(dataToExport);
        this.downloadCSV(csv, `licenses_${new Date().toISOString().split('T')[0]}.csv`);
        this.showSuccess('تم تصدير التراخيص بنجاح');
    }

    // الإجراءات المتعددة
    bulkActivate() {
        if (this.selectedLicenses.size === 0) return;

        this.selectedLicenses.forEach(licenseId => {
            const license = this.licenses.find(l => l.id === licenseId);
            if (license) {
                license.status = 'active';
            }
        });

        this.saveLicenses();
        this.loadLicenses();
        this.selectedLicenses.clear();
        this.showSuccess(`تم تفعيل ${this.selectedLicenses.size} ترخيص`);
    }

    bulkSuspend() {
        if (this.selectedLicenses.size === 0) return;

        this.selectedLicenses.forEach(licenseId => {
            const license = this.licenses.find(l => l.id === licenseId);
            if (license) {
                license.status = 'suspended';
            }
        });

        this.saveLicenses();
        this.loadLicenses();
        this.selectedLicenses.clear();
        this.showSuccess(`تم تعليق ${this.selectedLicenses.size} ترخيص`);
    }

    bulkDelete() {
        if (this.selectedLicenses.size === 0) return;

        if (confirm(`هل أنت متأكد من حذف ${this.selectedLicenses.size} ترخيص؟`)) {
            this.licenses = this.licenses.filter(l => !this.selectedLicenses.has(l.id));
            this.saveLicenses();
            this.loadLicenses();
            this.selectedLicenses.clear();
            this.showSuccess('تم حذف التراخيص المحددة');
        }
    }

    bulkExport() {
        if (this.selectedLicenses.size === 0) return;

        const selectedLicenses = this.licenses.filter(l => this.selectedLicenses.has(l.id));
        const dataToExport = selectedLicenses.map(license => ({
            'رقم الترخيص': license.key,
            'نوع الترخيص': this.getTypeText(license.type),
            'الحالة': this.getStatusText(license.status),
            'اسم العميل': `${license.clientInfo.firstName} ${license.clientInfo.lastName}`,
            'رقم الهاتف': license.clientInfo.phone,
            'تاريخ الإنشاء': this.formatDate(license.createdDate),
            'تاريخ الانتهاء': this.formatDate(license.expiryDate)
        }));

        const csv = this.convertToCSV(dataToExport);
        this.downloadCSV(csv, `selected_licenses_${new Date().toISOString().split('T')[0]}.csv`);
        this.showSuccess('تم تصدير التراخيص المحددة');
    }

    // دوال مساعدة
    convertToCSV(data) {
        if (data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');

        return csvContent;
    }

    downloadCSV(csv, filename) {
        const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    loadStatesData() {
        const stateSelect = document.getElementById('client-state');
        if (!stateSelect || !window.algeriaData) return;

        stateSelect.innerHTML = '<option value="">اختر الولاية</option>';

        const states = window.algeriaData.getAllStates();
        states.forEach(state => {
            const option = document.createElement('option');
            option.value = state.code;
            option.textContent = `${state.code} - ${state.name}`;
            stateSelect.appendChild(option);
        });
    }

    updateMunicipalities() {
        const stateSelect = document.getElementById('client-state');
        const municipalitySelect = document.getElementById('client-municipality');

        if (!stateSelect || !municipalitySelect || !window.algeriaData) return;

        const selectedStateCode = stateSelect.value;
        municipalitySelect.innerHTML = '<option value="">اختر البلدية</option>';

        if (selectedStateCode) {
            const municipalities = window.algeriaData.getMunicipalities(selectedStateCode);
            municipalities.forEach(municipality => {
                const option = document.createElement('option');
                option.value = municipality;
                option.textContent = municipality;
                municipalitySelect.appendChild(option);
            });
        }
    }

    resetCreateLicenseForm() {
        document.getElementById('create-license-form').reset();
        document.getElementById('license-duration').value = '365';
        document.getElementById('client-municipality').innerHTML = '<option value="">اختر البلدية</option>';
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showInfo(message) {
        this.showToast(message, 'info');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        // استخدام الأنماط من CSS بدلاً من inline styles
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            animation: slideInLeft 0.3s ease-out;
            max-width: 400px;
            display: flex;
            align-items: center;
            gap: 10px;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutLeft 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 4000);
    }
}

// ملاحظة: تم دمج LicenseManager مع AdminPanel لتجنب التضارب

// إضافة أنماط CSS للتنبيهات
const toastStyles = document.createElement('style');
toastStyles.textContent = `
    @keyframes slideInLeft {
        from {
            transform: translateX(-100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutLeft {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(-100%);
            opacity: 0;
        }
    }

    .toast-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .license-type-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .license-type-badge.demo {
        background: rgba(108, 117, 125, 0.1);
        color: #6c757d;
    }

    .license-type-badge.basic {
        background: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
    }

    .license-type-badge.premium {
        background: rgba(243, 156, 18, 0.1);
        color: #f39c12;
    }

    .license-type-badge.enterprise {
        background: rgba(142, 68, 173, 0.1);
        color: #8e44ad;
    }

    .days-remaining.expired {
        color: #e74c3c;
        font-weight: bold;
    }

    .days-remaining.expiring {
        color: #f39c12;
        font-weight: bold;
    }

    .days-remaining.warning {
        color: #f39c12;
    }

    .days-remaining.good {
        color: #27ae60;
    }

    .license-key-cell {
        display: flex;
        flex-direction: column;
    }

    .license-key-cell small {
        color: #666;
        font-size: 0.8rem;
    }

    .client-info {
        display: flex;
        flex-direction: column;
    }

    .client-info small {
        color: #666;
        font-size: 0.8rem;
    }
`;
document.head.appendChild(toastStyles);
