<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفعيل الترخيص التجريبي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            text-align: right;
        }
        .step h3 {
            color: #007bff;
            margin-bottom: 10px;
        }
        .code-box {
            background: #f1f3f4;
            border: 1px solid #dadce0;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
            direction: ltr;
            text-align: left;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 تفعيل الترخيص التجريبي</h1>
        
        <div class="warning">
            ⚠️ هذا ترخيص تجريبي صالح لمدة 30 يوماً للاختبار فقط
        </div>

        <div class="step">
            <h3>الخطوة 1: نسخ كود الترخيص</h3>
            <p>انسخ الكود التالي:</p>
            <div class="code-box" id="licenseCode">
localStorage.setItem('appLicense', '{"key":"FF-DEMO-2024-TEST","deviceId":"DEV-1734192000000-abc123def","activationDate":"2024-12-14T12:00:00.000Z","expiryDate":"2025-01-13T12:00:00.000Z","status":"active","type":"demo","clientInfo":{"firstName":"مستخدم","lastName":"تجريبي","phone":"**********","state":"الجزائر","municipality":"الجزائر الوسطى"},"signature":"RkYtREVNTy0yMDI0"}')
            </div>
            <button class="btn" onclick="copyToClipboard()">📋 نسخ الكود</button>
        </div>

        <div class="step">
            <h3>الخطوة 2: فتح أدوات المطور</h3>
            <p>في صفحة تسجيل الدخول، اضغط <strong>F12</strong> أو انقر بالزر الأيمن واختر "فحص العنصر"</p>
        </div>

        <div class="step">
            <h3>الخطوة 3: فتح وحدة التحكم</h3>
            <p>انقر على تبويب <strong>Console</strong> في أدوات المطور</p>
        </div>

        <div class="step">
            <h3>الخطوة 4: لصق الكود</h3>
            <p>الصق الكود المنسوخ في وحدة التحكم واضغط <strong>Enter</strong></p>
        </div>

        <div class="step">
            <h3>الخطوة 5: إعادة تحميل الصفحة</h3>
            <p>اضغط <strong>F5</strong> أو انقر على زر إعادة التحميل</p>
        </div>

        <div class="step">
            <h3>الخطوة 6: تسجيل الدخول</h3>
            <p>استخدم أي من بيانات الدخول التالية:</p>
            <ul style="text-align: right; list-style: none;">
                <li>👤 <strong>admin</strong> / 🔑 <strong>admin123</strong></li>
                <li>👤 <strong>user</strong> / 🔑 <strong>user123</strong></li>
                <li>👤 <strong>manager</strong> / 🔑 <strong>manager123</strong></li>
            </ul>
        </div>

        <div style="margin-top: 30px;">
            <button class="btn" onclick="window.open('src/auth/login.html', '_blank')">
                🚀 فتح صفحة تسجيل الدخول
            </button>
            <button class="btn" onclick="activateAutomatically()" style="background: #28a745;">
                ⚡ تفعيل تلقائي (إذا كنت في نفس النطاق)
            </button>
        </div>

        <div id="result" style="margin-top: 20px;"></div>
    </div>

    <script>
        function copyToClipboard() {
            const code = document.getElementById('licenseCode').textContent;
            navigator.clipboard.writeText(code).then(() => {
                alert('✅ تم نسخ الكود بنجاح!');
            }).catch(() => {
                // طريقة بديلة للنسخ
                const textArea = document.createElement('textarea');
                textArea.value = code;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('✅ تم نسخ الكود بنجاح!');
            });
        }

        function activateAutomatically() {
            try {
                const licenseData = {
                    "key": "FF-DEMO-2024-TEST",
                    "deviceId": "DEV-1734192000000-abc123def",
                    "activationDate": "2024-12-14T12:00:00.000Z",
                    "expiryDate": "2025-01-13T12:00:00.000Z",
                    "status": "active",
                    "type": "demo",
                    "clientInfo": {
                        "firstName": "مستخدم",
                        "lastName": "تجريبي",
                        "phone": "**********",
                        "state": "الجزائر",
                        "municipality": "الجزائر الوسطى"
                    },
                    "signature": "RkYtREVNTy0yMDI0"
                };

                localStorage.setItem('appLicense', JSON.stringify(licenseData));
                
                document.getElementById('result').innerHTML = 
                    '<div class="success">✅ تم تفعيل الترخيص بنجاح! يمكنك الآن تسجيل الدخول.</div>';
                
                // إعادة توجيه بعد 3 ثوان
                setTimeout(() => {
                    window.location.href = 'src/auth/login.html';
                }, 3000);
                
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div style="color: red;">❌ فشل التفعيل التلقائي. استخدم الطريقة اليدوية.</div>';
            }
        }

        // فحص حالة الترخيص الحالية
        window.onload = function() {
            const existingLicense = localStorage.getItem('appLicense');
            if (existingLicense) {
                try {
                    const license = JSON.parse(existingLicense);
                    const expiryDate = new Date(license.expiryDate);
                    const now = new Date();
                    
                    if (now < expiryDate) {
                        document.getElementById('result').innerHTML = 
                            '<div class="success">✅ يوجد ترخيص نشط بالفعل! يمكنك تسجيل الدخول مباشرة.</div>';
                    }
                } catch (e) {
                    // ترخيص تالف
                }
            }
        };
    </script>
</body>
</html>
