/* أنماط لوحة التحكم عن بُعد */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --sidebar-bg: #2c3e50;
    --sidebar-text: #ecf0f1;
    --main-bg: #f4f6f9;
    --card-bg: #ffffff;
    --border-color: #dee2e6;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    background: var(--main-bg);
    color: var(--dark-color);
    overflow-x: hidden;
}

/* الحاوي الرئيسي */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: var(--sidebar-bg);
    color: var(--sidebar-text);
    position: fixed;
    height: 100vh;
    right: 0;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
    overflow-y: auto;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.logo i {
    font-size: 2rem;
    color: var(--secondary-color);
}

.logo h2 {
    font-size: 1.3rem;
    font-weight: 600;
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
}

.admin-avatar {
    width: 45px;
    height: 45px;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.admin-avatar i {
    font-size: 1.5rem;
    color: white;
}

.admin-details {
    display: flex;
    flex-direction: column;
}

.admin-name {
    font-weight: 600;
    font-size: 1rem;
}

.admin-role {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* التنقل */
.sidebar-nav {
    padding: 20px 0;
}

.sidebar-nav ul {
    list-style: none;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: var(--transition);
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    border-right-color: var(--secondary-color);
}

.nav-item.active .nav-link {
    background: rgba(52, 152, 219, 0.2);
    border-right-color: var(--secondary-color);
    color: white;
}

.nav-link i {
    width: 20px;
    text-align: center;
}

/* فوتر الشريط الجانبي */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
    width: 100%;
    padding: 12px;
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.logout-btn:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    min-height: 100vh;
}

/* الهيدر */
.main-header {
    background: var(--card-bg);
    padding: 20px 30px;
    box-shadow: var(--shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--primary-color);
    cursor: pointer;
}

#page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--dark-color);
}

.stat-item i {
    color: var(--secondary-color);
}

.current-time {
    font-size: 0.9rem;
    color: var(--dark-color);
    font-weight: 500;
}

/* منطقة المحتوى */
.content-area {
    padding: 30px;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* لوحة المعلومات */
.dashboard-grid {
    display: grid;
    gap: 30px;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--card-bg);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
}

.stat-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: var(--primary-color);
}

.stat-card:nth-child(2) .stat-icon {
    background: var(--success-color);
}

.stat-card:nth-child(3) .stat-icon {
    background: var(--warning-color);
}

.stat-card:nth-child(4) .stat-icon {
    background: var(--info-color);
}

.stat-info h3 {
    font-size: 0.9rem;
    color: var(--dark-color);
    margin-bottom: 8px;
    opacity: 0.8;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

.stat-change.neutral {
    color: var(--info-color);
}

/* الرسوم البيانية */
.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-card {
    background: var(--card-bg);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.chart-card h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.1rem;
}

/* الأنشطة الأخيرة */
.recent-activities {
    background: var(--card-bg);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.recent-activities h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.activities-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
}

.activity-icon.success {
    background: var(--success-color);
}

.activity-icon.warning {
    background: var(--warning-color);
}

.activity-icon.info {
    background: var(--info-color);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 3px;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--dark-color);
    opacity: 0.7;
}

/* هيدر الأقسام */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.section-header h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.section-actions {
    display: flex;
    gap: 10px;
}

/* الأزرار */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn.primary {
    background: var(--primary-color);
    color: white;
}

.btn.secondary {
    background: var(--light-color);
    color: var(--primary-color);
    border: 1px solid var(--border-color);
}

.btn.success {
    background: var(--success-color);
    color: white;
}

.btn.danger {
    background: var(--danger-color);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* شريط الفلاتر */
.filters-bar {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 20px;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--primary-color);
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

/* الجداول */
.table-container {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--primary-color);
}

.data-table tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

/* شبكة العملاء */
.clients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.client-card {
    background: var(--card-bg);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.client-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

/* قائمة الطلبات */
.requests-list {
    display: grid;
    gap: 15px;
}

.request-card {
    background: var(--card-bg);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border-right: 4px solid var(--warning-color);
}

/* المراقبة */
.monitoring-dashboard {
    display: grid;
    gap: 20px;
}

.monitoring-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.monitor-card {
    background: var(--card-bg);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
}

.status-indicator.online {
    color: var(--success-color);
}

.usage-bar {
    width: 100%;
    height: 8px;
    background: var(--light-color);
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.usage-fill {
    height: 100%;
    background: var(--success-color);
    transition: var(--transition);
}

/* السجلات المباشرة */
.real-time-logs {
    background: var(--card-bg);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.logs-container {
    max-height: 400px;
    overflow-y: auto;
    background: #1e1e1e;
    color: #00ff00;
    padding: 15px;
    border-radius: var(--border-radius);
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    line-height: 1.4;
}

/* تبويبات الإعدادات */
.settings-tabs {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.tab-buttons {
    display: flex;
    background: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--primary-color);
    transition: var(--transition);
}

.tab-btn.active {
    background: var(--card-bg);
    border-bottom: 2px solid var(--secondary-color);
}

.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

.settings-form {
    max-width: 500px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--primary-color);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-left: 8px;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--dark-color);
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
}

.close-btn:hover {
    opacity: 1;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* التجاوب */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .header-stats {
        display: none;
    }
}

@media (max-width: 768px) {
    .content-area {
        padding: 20px 15px;
    }
    
    .main-header {
        padding: 15px 20px;
    }
    
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .filters-bar {
        flex-direction: column;
        gap: 15px;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .section-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .tab-buttons {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: none;
        min-width: 120px;
    }
}

/* أنماط إدارة التراخيص المطورة */

/* شبكة إحصائيات التراخيص */
.license-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
}

.stat-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.stat-card.active {
    border-left-color: var(--success-color);
}

.stat-card.expired {
    border-left-color: var(--danger-color);
}

.stat-card.suspended {
    border-left-color: var(--warning-color);
}

.stat-card.expiring {
    border-left-color: var(--info-color);
}

.stat-card .stat-icon {
    display: inline-block;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.stat-card.active .stat-icon {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.stat-card.expired .stat-icon {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.stat-card.suspended .stat-icon {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.stat-card.expiring .stat-icon {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.stat-card h3 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 10px;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.stat-card .stat-change {
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.stat-change.positive {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.stat-change.negative {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.stat-change.neutral {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.stat-change.warning {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

/* شريط الإجراءات المتعددة */
.bulk-actions-bar {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
}

.selected-count {
    font-weight: 600;
    color: var(--primary-color);
}

.bulk-actions {
    display: flex;
    gap: 10px;
}

.bulk-actions .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* النوافذ المنبثقة للتراخيص */
.license-modal-content {
    max-width: 800px;
    width: 90%;
}

.license-details-content {
    max-width: 600px;
    width: 90%;
}

.delete-modal-content {
    max-width: 500px;
    width: 90%;
}

.license-form {
    padding: 20px 0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* تفاصيل الترخيص */
.license-details {
    padding: 20px 0;
}

.license-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.license-detail-item:last-child {
    border-bottom: none;
}

.license-detail-label {
    font-weight: 600;
    color: var(--dark-color);
}

.license-detail-value {
    color: #666;
}

.license-status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.license-status-badge.active {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.license-status-badge.expired {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.license-status-badge.suspended {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

/* تأكيد الحذف */
.delete-confirmation {
    text-align: center;
    padding: 20px 0;
}

.delete-confirmation p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.delete-confirmation .license-info {
    background: var(--light-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    margin: 20px 0;
}

.warning-text {
    color: var(--danger-color);
    font-weight: 600;
    font-size: 0.9rem;
}

/* أزرار الإجراءات في الجدول */
.action-buttons {
    display: flex;
    gap: 5px;
}

.action-btn {
    padding: 6px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
}

.action-btn.view {
    background: var(--info-color);
    color: white;
}

.action-btn.edit {
    background: var(--warning-color);
    color: white;
}

.action-btn.delete {
    background: var(--danger-color);
    color: white;
}

.action-btn:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}
