const { app, BrowserWindow, Menu, dialog, ipcMain, shell, nativeTheme } = require('electron');
const { autoUpdater } = require('electron-updater');
const log = require('electron-log');
const path = require('path');

// إعداد نظام السجلات
log.transports.file.level = 'info';
autoUpdater.logger = log;

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env.NODE_ENV = 'production';

// المتغير العام للنافذة الرئيسية
let mainWindow;

// التحقق من وجود نسخة واحدة فقط من التطبيق
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // إذا حاول المستخدم فتح نسخة ثانية، ركز على النافذة الموجودة
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

// فحص حالة تسجيل الدخول
function checkLoginStatus() {
  try {
    const fs = require('fs');
    const sessionPath = path.join(__dirname, 'data', 'session.json');

    if (fs.existsSync(sessionPath)) {
      const sessionData = JSON.parse(fs.readFileSync(sessionPath, 'utf8'));
      const now = new Date();
      const sessionExpiry = new Date(sessionData.expiryDate);

      return now < sessionExpiry && sessionData.isValid;
    }
    return false;
  } catch (error) {
    console.error('خطأ في فحص حالة تسجيل الدخول:', error);
    return false;
  }
}

// إنشاء النافذة الرئيسية
function createMainWindow() {
  const isLoggedIn = checkLoginStatus();

  mainWindow = new BrowserWindow({
    title: isLoggedIn ? 'نظام إدارة مؤسسة وقود المستقبل' : 'تسجيل الدخول - مؤسسة وقود المستقبل',
    width: isLoggedIn ? 1200 : 800,
    height: isLoggedIn ? 800 : 600,
    minWidth: isLoggedIn ? 800 : 600,
    minHeight: isLoggedIn ? 600 : 500,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      allowRunningInsecureContent: false
    },
    icon: path.join(__dirname, 'assets/icons/app-icon.ico'),
    show: false, // لا تظهر النافذة حتى تكتمل عملية التحميل
    titleBarStyle: 'default',
    frame: true,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true,
    center: true
  });

  // تحميل الصفحة المناسبة
  if (isLoggedIn) {
    mainWindow.loadFile('index.html');
  } else {
    mainWindow.loadFile('src/auth/login.html');
  }

  // إظهار النافذة عند اكتمال التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // إنشاء قائمة التطبيق
  const mainMenu = Menu.buildFromTemplate(menuTemplate);
  Menu.setApplicationMenu(mainMenu);

  // إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إنشاء قائمة التطبيق
const menuTemplate = [
  {
    label: 'ملف',
    submenu: [
      {
        label: 'حفظ البيانات',
        accelerator: process.platform === 'darwin' ? 'Command+S' : 'Ctrl+S',
        click() {
          mainWindow.webContents.executeJavaScript('saveData()');
        }
      },
      {
        label: 'طباعة',
        accelerator: process.platform === 'darwin' ? 'Command+P' : 'Ctrl+P',
        click() {
          mainWindow.webContents.print();
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'خروج',
        accelerator: process.platform === 'darwin' ? 'Command+Q' : 'Ctrl+Q',
        click() {
          app.quit();
        }
      }
    ]
  },
  {
    label: 'عرض',
    submenu: [
      {
        label: 'إعادة تحميل',
        accelerator: 'F5',
        click() {
          mainWindow.reload();
        }
      },
      {
        label: 'تكبير',
        accelerator: process.platform === 'darwin' ? 'Command+Plus' : 'Ctrl+Plus',
        click() {
          mainWindow.webContents.setZoomLevel(mainWindow.webContents.getZoomLevel() + 1);
        }
      },
      {
        label: 'تصغير',
        accelerator: process.platform === 'darwin' ? 'Command+-' : 'Ctrl+-',
        click() {
          mainWindow.webContents.setZoomLevel(mainWindow.webContents.getZoomLevel() - 1);
        }
      },
      {
        label: 'حجم طبيعي',
        accelerator: process.platform === 'darwin' ? 'Command+0' : 'Ctrl+0',
        click() {
          mainWindow.webContents.setZoomLevel(0);
        }
      }
    ]
  },
  {
    label: 'مساعدة',
    submenu: [
      {
        label: 'حول البرنامج',
        click() {
          dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: 'حول البرنامج',
            message: 'نظام إدارة مؤسسة وقود المستقبل',
            detail: 'الإصدار 2.2.0\nنظام متكامل لإدارة مؤسسة وقود المستقبل\n\nالميزات الجديدة:\n• نسخ احتياطية تلقائية\n• تكامل تيليجرام\n• اختصارات لوحة المفاتيح\n• الوضع المظلم'
          });
        }
      }
    ]
  }
];

// معالجة أحداث IPC
const setupIpcHandlers = () => {
  // فتح مربع حوار حفظ الملف
  ipcMain.handle('save-dialog', async (_, options) => {
    const { canceled, filePath } = await dialog.showSaveDialog(mainWindow, options);
    if (canceled) {
      return null;
    }
    return filePath;
  });

  // فتح مربع حوار فتح الملف
  ipcMain.handle('open-dialog', async (_, options) => {
    const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, options);
    if (canceled) {
      return null;
    }
    return filePaths[0];
  });

  // إنشاء نسخة احتياطية تلقائية
  ipcMain.on('create-auto-backup', () => {
    log.info('إنشاء نسخة احتياطية تلقائية...');
    // يمكن إضافة كود لإنشاء نسخة احتياطية تلقائية هنا
  });

  // فتح رابط خارجي
  ipcMain.handle('open-external', async (_, url) => {
    await shell.openExternal(url);
  });

  // الحصول على معلومات النظام
  ipcMain.handle('get-system-info', () => {
    return {
      platform: process.platform,
      arch: process.arch,
      version: app.getVersion(),
      electronVersion: process.versions.electron,
      nodeVersion: process.versions.node
    };
  });

  // تغيير الثيم
  ipcMain.handle('set-theme', (_, theme) => {
    nativeTheme.themeSource = theme;
    return nativeTheme.shouldUseDarkColors;
  });

  // الحصول على الثيم الحالي
  ipcMain.handle('get-theme', () => {
    return {
      shouldUseDarkColors: nativeTheme.shouldUseDarkColors,
      themeSource: nativeTheme.themeSource
    };
  });

  // معالجات نظام تسجيل الدخول
  ipcMain.handle('login', async (_, credentials) => {
    try {
      // محاكاة التحقق من بيانات الاعتماد
      const validCredentials = [
        { username: 'admin', password: 'admin123' },
        { username: 'user', password: 'user123' },
        { username: 'manager', password: 'manager123' }
      ];

      const isValid = validCredentials.some(cred =>
        cred.username === credentials.username && cred.password === credentials.password
      );

      if (isValid) {
        // إنشاء جلسة
        const sessionData = {
          username: credentials.username,
          loginTime: new Date().toISOString(),
          expiryDate: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(), // 8 ساعات
          isValid: true
        };

        // حفظ الجلسة
        const fs = require('fs');
        const dataDir = path.join(__dirname, 'data');
        if (!fs.existsSync(dataDir)) {
          fs.mkdirSync(dataDir, { recursive: true });
        }

        fs.writeFileSync(
          path.join(dataDir, 'session.json'),
          JSON.stringify(sessionData, null, 2)
        );

        return { success: true, user: { username: credentials.username } };
      } else {
        return { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
      }
    } catch (error) {
      return { success: false, error: 'حدث خطأ أثناء تسجيل الدخول' };
    }
  });

  // تسجيل الخروج
  ipcMain.handle('logout', async () => {
    try {
      const fs = require('fs');
      const sessionPath = path.join(__dirname, 'data', 'session.json');

      if (fs.existsSync(sessionPath)) {
        fs.unlinkSync(sessionPath);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ أثناء تسجيل الخروج' };
    }
  });

  // إعادة تحميل التطبيق بعد تسجيل الدخول
  ipcMain.handle('reload-after-login', () => {
    mainWindow.loadFile('index.html');
    mainWindow.setSize(1200, 800);
    mainWindow.center();
    mainWindow.setTitle('نظام إدارة مؤسسة وقود المستقبل');
  });

  // إرسال طلب تفعيل
  ipcMain.handle('submit-activation-request', async (_, requestData) => {
    try {
      // حفظ الطلب محلياً
      const fs = require('fs');
      const dataDir = path.join(__dirname, 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      const requestsFile = path.join(dataDir, 'activation-requests.json');
      let requests = [];

      if (fs.existsSync(requestsFile)) {
        requests = JSON.parse(fs.readFileSync(requestsFile, 'utf8'));
      }

      const newRequest = {
        ...requestData,
        id: Date.now(),
        timestamp: new Date().toISOString(),
        status: 'pending'
      };

      requests.push(newRequest);
      fs.writeFileSync(requestsFile, JSON.stringify(requests, null, 2));

      // في التطبيق الحقيقي، سيتم إرسال الطلب إلى الخادم
      return { success: true, requestId: newRequest.id };
    } catch (error) {
      return { success: false, error: 'فشل في إرسال طلب التفعيل' };
    }
  });
};

// إعداد نظام التحديث التلقائي
const setupAutoUpdater = () => {
  // التحقق من التحديثات عند بدء التطبيق
  autoUpdater.checkForUpdatesAndNotify();

  // أحداث التحديث
  autoUpdater.on('checking-for-update', () => {
    log.info('جاري البحث عن تحديثات...');
  });

  autoUpdater.on('update-available', (info) => {
    log.info('تحديث متوفر:', info);
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'تحديث متوفر',
      message: 'يتوفر إصدار جديد من التطبيق',
      detail: `الإصدار الجديد: ${info.version}\nسيتم تنزيل التحديث في الخلفية.`,
      buttons: ['موافق']
    });
  });

  autoUpdater.on('update-not-available', (info) => {
    log.info('لا توجد تحديثات متوفرة:', info);
  });

  autoUpdater.on('error', (err) => {
    log.error('خطأ في التحديث:', err);
  });

  autoUpdater.on('download-progress', (progressObj) => {
    let log_message = `سرعة التنزيل: ${progressObj.bytesPerSecond}`;
    log_message = log_message + ` - تم تنزيل ${progressObj.percent}%`;
    log_message = log_message + ` (${progressObj.transferred}/${progressObj.total})`;
    log.info(log_message);
  });

  autoUpdater.on('update-downloaded', (info) => {
    log.info('تم تنزيل التحديث:', info);
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'تحديث جاهز',
      message: 'تم تنزيل التحديث بنجاح',
      detail: 'سيتم إعادة تشغيل التطبيق لتطبيق التحديث.',
      buttons: ['إعادة التشغيل الآن', 'لاحقاً']
    }).then((result) => {
      if (result.response === 0) {
        autoUpdater.quitAndInstall();
      }
    });
  });
};

// معالجات IPC إضافية لـ Electron
const setupElectronIpcHandlers = () => {
  // معلومات النظام
  ipcMain.handle('get-system-info', () => {
    return {
      platform: process.platform,
      arch: process.arch,
      version: app.getVersion(),
      electronVersion: process.versions.electron,
      nodeVersion: process.versions.node
    };
  });

  // حفظ وتحميل بيانات الزبائن
  ipcMain.handle('save-customer-data', async (event, customers) => {
    try {
      const userDataPath = app.getPath('userData');
      const dataDir = path.join(userDataPath, 'data');
      await fs.mkdir(dataDir, { recursive: true });
      const customersPath = path.join(dataDir, 'customers.json');
      await fs.writeFile(customersPath, JSON.stringify(customers, null, 2));
      return true;
    } catch (error) {
      console.error('خطأ في حفظ بيانات الزبائن:', error);
      return false;
    }
  });

  ipcMain.handle('load-customer-data', async () => {
    try {
      const userDataPath = app.getPath('userData');
      const customersPath = path.join(userDataPath, 'data', 'customers.json');
      const data = await fs.readFile(customersPath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('خطأ في تحميل بيانات الزبائن:', error);
      return [];
    }
  });

  // حفظ وتحميل بيانات التراخيص
  ipcMain.handle('save-license-data', async (event, licenses) => {
    try {
      const userDataPath = app.getPath('userData');
      const dataDir = path.join(userDataPath, 'data');
      await fs.mkdir(dataDir, { recursive: true });
      const licensesPath = path.join(dataDir, 'licenses.json');
      await fs.writeFile(licensesPath, JSON.stringify(licenses, null, 2));
      return true;
    } catch (error) {
      console.error('خطأ في حفظ بيانات التراخيص:', error);
      return false;
    }
  });

  ipcMain.handle('load-license-data', async () => {
    try {
      const userDataPath = app.getPath('userData');
      const licensesPath = path.join(userDataPath, 'data', 'licenses.json');
      const data = await fs.readFile(licensesPath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('خطأ في تحميل بيانات التراخيص:', error);
      return [];
    }
  });

  // حفظ وتحميل الإحصائيات
  ipcMain.handle('save-statistics', async (event, stats) => {
    try {
      const userDataPath = app.getPath('userData');
      const dataDir = path.join(userDataPath, 'data');
      await fs.mkdir(dataDir, { recursive: true });
      const statsPath = path.join(dataDir, 'statistics.json');
      await fs.writeFile(statsPath, JSON.stringify(stats, null, 2));
      return true;
    } catch (error) {
      console.error('خطأ في حفظ الإحصائيات:', error);
      return false;
    }
  });

  ipcMain.handle('load-statistics', async () => {
    try {
      const userDataPath = app.getPath('userData');
      const statsPath = path.join(userDataPath, 'data', 'statistics.json');
      const data = await fs.readFile(statsPath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('خطأ في تحميل الإحصائيات:', error);
      return {
        totalCustomers: 0,
        totalLicenses: 0,
        totalRevenue: 0,
        monthlyStats: []
      };
    }
  });

  // إشعارات النظام
  ipcMain.handle('show-notification', (event, title, body, icon) => {
    new Notification({
      title,
      body,
      icon: icon || path.join(__dirname, 'assets/icons/app-icon.png')
    }).show();
  });

  // تغيير الثيم
  ipcMain.handle('set-theme', (event, theme) => {
    console.log('تم تغيير الثيم إلى:', theme);
  });

  console.log('✅ تم تحميل معالجات IPC الإضافية لـ Electron');
};

// تشغيل التطبيق
app.whenReady().then(() => {
  setupIpcHandlers();
  setupElectronIpcHandlers();
  createMainWindow();
  setupAutoUpdater();
});

// إغلاق التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// إعادة إنشاء النافذة عند تفعيل التطبيق (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  }
});